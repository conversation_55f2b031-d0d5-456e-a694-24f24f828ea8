"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ActionTable, ActionStatsCards } from "@/components/features/actions"
import { useActions, useActionStats } from "@/hooks/use-actions"
import { useActionActions } from "@/hooks/use-action-actions"
import { useActionPermissions } from "@/hooks/use-action-permissions"
import { ActionStatus, ActionPriority, ActionFilters } from "@/lib/validations/action"
import { Plus, Search, Filter, AlertTriangle, CheckCircle, Clock } from "lucide-react"
import { useRouter } from "next/navigation"

export default function ActionsPage() {
  const router = useRouter()
  const [view, setView] = useState<"table" | "cards">("table")
  const [searchTerm, setSearchTerm] = useState("")

  const {
    canRead,
    canCreate,
    canUpdate,
    canDelete,
    canComplete
  } = useActionPermissions()

  const {
    actions,
    loading,
    error,
    total,
    page,
    totalPages,
    refetch,
    setFilters,
    filters
  } = useActions({
    initialFilters: {
      page: 1,
      limit: 20,
      sortBy: "dueDate",
      sortOrder: "asc"
    }
  })

  const {
    stats,
    loading: statsLoading,
    error: statsError
  } = useActionStats()

  const {
    loading: actionLoading,
    error: actionError,
    completeAction,
    startAction,
    reopenAction,
    deleteAction
  } = useActionActions()

  // Handlers pour les actions
  const handleViewAction = (action: any) => {
    router.push(`/actions/${action.id}`)
  }

  const handleEditAction = (action: any) => {
    router.push(`/actions/${action.id}/edit`)
  }

  const handleDeleteAction = async (action: any) => {
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'action "${action.title}" ?`)) {
      const success = await deleteAction(action.id)
      if (success) {
        await refetch()
      }
    }
  }

  const handleCompleteAction = async (action: any) => {
    if (confirm(`Marquer l'action "${action.title}" comme terminée ?`)) {
      const completed = await completeAction(action.id)
      if (completed) {
        await refetch()
      }
    }
  }

  const handleStartAction = async (action: any) => {
    if (confirm(`Démarrer l'action "${action.title}" ?`)) {
      const started = await startAction(action.id)
      if (started) {
        await refetch()
      }
    }
  }

  const handleReopenAction = async (action: any) => {
    if (confirm(`Rouvrir l'action "${action.title}" ?`)) {
      const reopened = await reopenAction(action.id)
      if (reopened) {
        await refetch()
      }
    }
  }

  // Handlers pour les filtres
  const handleSearch = () => {
    setFilters({ search: searchTerm, page: 1 })
  }

  const handleStatusFilter = (status: string) => {
    setFilters({ 
      status: status === "all" ? undefined : status as ActionStatus,
      page: 1 
    })
  }

  const handlePriorityFilter = (priority: string) => {
    setFilters({ 
      priority: priority === "all" ? undefined : priority as ActionPriority,
      page: 1 
    })
  }

  const handlePageChange = (newPage: number) => {
    setFilters({ page: newPage })
  }

  if (!canRead()) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Vous n'avez pas les permissions nécessaires pour voir les actions.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold magneto-title">Actions correctives</h1>
          <p className="text-gray-600">
            Gérez les actions correctives et leur suivi
          </p>
        </div>
        
        {canCreate() && (
          <Button
            onClick={() => router.push("/actions/new")}
            className="magneto-button"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouvelle action
          </Button>
        )}
      </div>

      {/* Statistiques */}
      {stats && (
        <ActionStatsCards stats={stats} loading={statsLoading} />
      )}

      {/* Filtres et recherche */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtres et recherche
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex gap-2">
              <Input
                placeholder="Rechercher une action..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button onClick={handleSearch} variant="outline">
                <Search className="h-4 w-4" />
              </Button>
            </div>

            <Select
              value={filters.status || "all"}
              onValueChange={handleStatusFilter}
            >
              <SelectTrigger>
                <SelectValue placeholder="Statut" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les statuts</SelectItem>
                <SelectItem value={ActionStatus.PENDING}>En attente</SelectItem>
                <SelectItem value={ActionStatus.IN_PROGRESS}>En cours</SelectItem>
                <SelectItem value={ActionStatus.COMPLETED}>Terminées</SelectItem>
                <SelectItem value={ActionStatus.OVERDUE}>En retard</SelectItem>
                <SelectItem value={ActionStatus.CANCELLED}>Annulées</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.priority || "all"}
              onValueChange={handlePriorityFilter}
            >
              <SelectTrigger>
                <SelectValue placeholder="Priorité" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes les priorités</SelectItem>
                <SelectItem value={ActionPriority.CRITICAL}>Critique</SelectItem>
                <SelectItem value={ActionPriority.HIGH}>Élevée</SelectItem>
                <SelectItem value={ActionPriority.MEDIUM}>Moyenne</SelectItem>
                <SelectItem value={ActionPriority.LOW}>Faible</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">Vue:</span>
              <Button
                variant={view === "table" ? "default" : "outline"}
                size="sm"
                onClick={() => setView("table")}
              >
                Tableau
              </Button>
              <Button
                variant={view === "cards" ? "default" : "outline"}
                size="sm"
                onClick={() => setView("cards")}
              >
                Cartes
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Erreurs */}
      {(error || actionError) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error || actionError}
          </AlertDescription>
        </Alert>
      )}

      {/* Liste des actions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>
              Actions ({total})
            </CardTitle>
            <div className="text-sm text-gray-500">
              Page {page} sur {totalPages}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <ActionTable
            actions={actions}
            onView={handleViewAction}
            onEdit={canUpdate() ? handleEditAction : undefined}
            onDelete={canDelete() ? handleDeleteAction : undefined}
            onComplete={canComplete() ? handleCompleteAction : undefined}
            onStart={canUpdate() ? handleStartAction : undefined}
            onReopen={canUpdate() ? handleReopenAction : undefined}
            loading={loading || actionLoading}
            showAuditInfo={true}
            showObservationInfo={true}
          />

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(page - 1)}
                disabled={page <= 1}
              >
                Précédent
              </Button>
              
              <span className="text-sm text-gray-500">
                Page {page} sur {totalPages}
              </span>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(page + 1)}
                disabled={page >= totalPages}
              >
                Suivant
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
