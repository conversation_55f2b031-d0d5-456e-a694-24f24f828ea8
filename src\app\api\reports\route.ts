import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ReportService } from "@/lib/services/report-service"
import { createReportSchema, reportFiltersSchema } from "@/lib/validations/report"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"
import { z } from "zod"

/**
 * GET /api/reports - Obtenir la liste des rapports
 */
export async function GET(request: NextRequest) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'reports', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Parser les paramètres de requête
      const { searchParams } = new URL(req.url)
      const filters = reportFiltersSchema.parse({
        status: searchParams.get('status') || undefined,
        type: searchParams.get('type') || undefined,
        organizationId: searchParams.get('organizationId') || undefined,
        auditId: searchParams.get('auditId') || undefined,
        creatorId: searchParams.get('creatorId') || undefined,
        createdFrom: searchParams.get('createdFrom') || undefined,
        createdTo: searchParams.get('createdTo') || undefined,
        publishedFrom: searchParams.get('publishedFrom') || undefined,
        publishedTo: searchParams.get('publishedTo') || undefined,
        search: searchParams.get('search') || undefined,
        page: searchParams.get('page') || 1,
        limit: searchParams.get('limit') || 10,
        sortBy: searchParams.get('sortBy') || 'createdAt',
        sortOrder: searchParams.get('sortOrder') || 'desc'
      })

      // Si l'utilisateur n'est pas admin, filtrer par son organisation
      if (user.role !== "SUPER_ADMIN" && user.organizationId) {
        filters.organizationId = user.organizationId
      }

      // Si l'utilisateur est un auditeur simple, ne voir que ses rapports
      if (user.role === "AUDITOR") {
        filters.creatorId = user.id
      }

      const result = await ReportService.getReports(filters)

      return NextResponse.json({
        success: true,
        data: result
      })

    } catch (error) {
      console.error("Erreur lors de la récupération des rapports:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Paramètres invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * POST /api/reports - Créer un nouveau rapport
 */
export async function POST(request: NextRequest) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'reports', 'create')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      const body = await req.json()
      const validatedData = createReportSchema.parse(body)

      // Si l'utilisateur n'est pas admin, forcer son organisation
      if (user.role !== "SUPER_ADMIN" && user.organizationId) {
        validatedData.organizationId = user.organizationId
      }

      const report = await ReportService.createReport(validatedData, user.id)

      return NextResponse.json({
        success: true,
        data: report
      }, { status: 201 })

    } catch (error) {
      console.error("Erreur lors de la création du rapport:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Données invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
