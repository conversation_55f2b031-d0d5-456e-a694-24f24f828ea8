# Guide de Test - Système de Gestion des Actions

Ce document décrit les procédures de test pour valider le système de gestion des actions correctives dans Magneto.

## Vue d'ensemble des tests

Le système d'actions doit être testé selon le workflow complet :
**Création → Assignation → Suivi → Résolution**

## Prérequis

### Données de test nécessaires
1. **Utilisateurs** avec différents rôles :
   - Super Admin
   - Admin
   - Manager
   - Auditor
   - User

2. **Audits** en cours avec :
   - Observations ouvertes
   - Différents statuts

3. **Organisations** configurées

### Accès requis
- Accès au tableau de bord Magneto
- Permissions appropriées selon le rôle
- Navigateur web moderne

## Tests fonctionnels

### 1. Création d'actions

#### Test 1.1 : Création depuis la page Actions
**Objectif** : Vérifier la création d'une nouvelle action

**Étapes** :
1. Se connecter avec un utilisateur ayant les permissions `actions:create`
2. Naviguer vers `/actions`
3. Cliquer sur "Nouvelle action"
4. Remplir le formulaire :
   - Titre : "Test - Correction documentation"
   - Description : "Mettre à jour la documentation de sécurité"
   - Priorité : "Élevée"
   - Échéance : Date dans 7 jours
   - Audit : Sélectionner un audit existant
   - Assigné : Sélectionner un utilisateur

**Résultat attendu** :
- ✅ Formulaire validé sans erreur
- ✅ Redirection vers la page de détail de l'action
- ✅ Action visible dans la liste des actions
- ✅ Notification envoyée à l'assigné

#### Test 1.2 : Création depuis un audit
**Objectif** : Vérifier la création d'action depuis un audit

**Étapes** :
1. Naviguer vers un audit spécifique
2. Aller dans l'onglet "Actions"
3. Cliquer sur "Nouvelle action"
4. Vérifier que l'audit est pré-sélectionné
5. Compléter et soumettre le formulaire

**Résultat attendu** :
- ✅ Audit pré-rempli et non modifiable
- ✅ Action créée et liée à l'audit
- ✅ Action visible dans l'onglet Actions de l'audit

#### Test 1.3 : Création depuis une observation
**Objectif** : Vérifier la création d'action depuis une observation

**Étapes** :
1. Naviguer vers une observation
2. Dans le menu déroulant, cliquer sur "Créer une action"
3. Vérifier que l'audit et l'observation sont pré-sélectionnés
4. Compléter et soumettre le formulaire

**Résultat attendu** :
- ✅ Audit et observation pré-remplis
- ✅ Action créée et liée à l'observation
- ✅ Action visible dans l'onglet Actions de l'observation

### 2. Assignation et permissions

#### Test 2.1 : Permissions de création
**Objectif** : Vérifier les permissions de création

**Étapes** :
1. Tester avec différents rôles :
   - Super Admin : ✅ Peut créer
   - Admin : ✅ Peut créer
   - Manager : ✅ Peut créer
   - Auditor : ✅ Peut créer
   - User : ❌ Ne peut pas créer

#### Test 2.2 : Assignation d'actions
**Objectif** : Vérifier l'assignation d'actions

**Étapes** :
1. Créer une action assignée à un utilisateur A
2. Se connecter en tant qu'utilisateur A
3. Vérifier que l'action apparaît dans "Mes actions"
4. Changer l'assigné vers utilisateur B
5. Vérifier les notifications

**Résultat attendu** :
- ✅ Action visible pour l'assigné
- ✅ Notification envoyée lors du changement
- ✅ Ancienne assignation supprimée

### 3. Suivi et gestion

#### Test 3.1 : Changement de statut
**Objectif** : Vérifier les transitions de statut

**Étapes** :
1. Créer une action (statut : En attente)
2. Démarrer l'action → En cours
3. Terminer l'action → Terminée
4. Rouvrir l'action → En attente

**Résultat attendu** :
- ✅ Transitions de statut correctes
- ✅ Dates de modification mises à jour
- ✅ Historique conservé

#### Test 3.2 : Gestion des échéances
**Objectif** : Vérifier la gestion des échéances

**Étapes** :
1. Créer des actions avec différentes échéances :
   - Action en retard (échéance passée)
   - Action due aujourd'hui
   - Action due dans 3 jours
   - Action due dans 2 semaines

2. Vérifier l'affichage dans le tableau de bord des échéances

**Résultat attendu** :
- ✅ Actions classées par urgence
- ✅ Couleurs appropriées (rouge, orange, jaune)
- ✅ Compteurs corrects

### 4. Notifications et alertes

#### Test 4.1 : Notifications d'assignation
**Objectif** : Vérifier les notifications d'assignation

**Étapes** :
1. Créer une action assignée à un utilisateur
2. Se connecter en tant qu'assigné
3. Vérifier la cloche de notifications

**Résultat attendu** :
- ✅ Notification "Nouvelle action assignée"
- ✅ Badge de compteur mis à jour
- ✅ Clic redirige vers l'action

#### Test 4.2 : Notifications d'échéance
**Objectif** : Vérifier les alertes d'échéance

**Étapes** :
1. Déclencher les vérifications de notifications
2. Vérifier les notifications pour :
   - Actions en retard
   - Actions dues bientôt

**Résultat attendu** :
- ✅ Notifications générées automatiquement
- ✅ Messages appropriés
- ✅ Pas de doublons

### 5. Interface utilisateur

#### Test 5.1 : Tableau des actions
**Objectif** : Vérifier l'affichage et les fonctionnalités du tableau

**Étapes** :
1. Naviguer vers `/actions`
2. Tester les filtres :
   - Par statut
   - Par priorité
   - Par recherche textuelle
3. Tester les actions rapides :
   - Voir, Modifier, Supprimer
   - Démarrer, Terminer, Rouvrir

**Résultat attendu** :
- ✅ Filtres fonctionnels
- ✅ Actions rapides disponibles selon permissions
- ✅ Pagination fonctionnelle

#### Test 5.2 : Page de détail
**Objectif** : Vérifier la page de détail d'action

**Étapes** :
1. Ouvrir une action existante
2. Vérifier l'affichage :
   - Informations complètes
   - Contexte (audit, observation)
   - Actions disponibles
   - Historique

**Résultat attendu** :
- ✅ Toutes les informations affichées
- ✅ Liens vers audit/observation fonctionnels
- ✅ Actions contextuelles disponibles

### 6. Tests d'intégration

#### Test 6.1 : Workflow complet
**Objectif** : Tester le workflow de bout en bout

**Scénario** :
1. **Création** : Auditeur crée une action depuis une observation
2. **Assignation** : Manager assigne l'action à un technicien
3. **Démarrage** : Technicien démarre l'action
4. **Suivi** : Manager suit l'avancement
5. **Completion** : Technicien termine l'action
6. **Validation** : Manager valide la completion

**Résultat attendu** :
- ✅ Workflow fluide sans erreur
- ✅ Notifications appropriées à chaque étape
- ✅ Permissions respectées
- ✅ Données cohérentes

#### Test 6.2 : Intégration avec audits
**Objectif** : Vérifier l'intégration avec le système d'audit

**Étapes** :
1. Créer des actions pour un audit
2. Vérifier l'affichage dans l'onglet Actions de l'audit
3. Terminer toutes les actions
4. Vérifier l'impact sur le statut de l'audit

**Résultat attendu** :
- ✅ Actions visibles dans l'audit
- ✅ Statistiques mises à jour
- ✅ Cohérence des données

## Tests de performance

### Test P1 : Chargement des listes
**Objectif** : Vérifier les performances de chargement

**Étapes** :
1. Créer 100+ actions
2. Mesurer le temps de chargement de `/actions`
3. Tester les filtres et la pagination

**Critères** :
- ✅ Chargement < 2 secondes
- ✅ Filtres réactifs < 500ms
- ✅ Pagination fluide

### Test P2 : Notifications en masse
**Objectif** : Tester la génération de notifications

**Étapes** :
1. Créer de nombreuses actions avec échéances variées
2. Déclencher les vérifications de notifications
3. Mesurer le temps de traitement

**Critères** :
- ✅ Traitement < 30 secondes pour 1000 actions
- ✅ Pas d'erreurs de timeout
- ✅ Mémoire stable

## Tests de sécurité

### Test S1 : Contrôle d'accès
**Objectif** : Vérifier la sécurité des permissions

**Étapes** :
1. Tenter d'accéder aux actions sans permissions
2. Tenter de modifier une action d'un autre utilisateur
3. Tester l'injection dans les formulaires

**Résultat attendu** :
- ✅ Accès refusé sans permissions
- ✅ Modifications bloquées
- ✅ Validation côté serveur active

### Test S2 : Validation des données
**Objectif** : Vérifier la validation des entrées

**Étapes** :
1. Soumettre des données invalides
2. Tester les limites de caractères
3. Vérifier l'échappement HTML

**Résultat attendu** :
- ✅ Validation côté client et serveur
- ✅ Messages d'erreur appropriés
- ✅ Pas d'injection possible

## Checklist de validation

### Fonctionnalités core
- [ ] Création d'actions (3 méthodes)
- [ ] Assignation et réassignation
- [ ] Gestion des statuts
- [ ] Gestion des échéances
- [ ] Notifications automatiques
- [ ] Permissions et sécurité

### Interface utilisateur
- [ ] Navigation intuitive
- [ ] Formulaires validés
- [ ] Tableaux fonctionnels
- [ ] Responsive design
- [ ] Accessibilité

### Intégrations
- [ ] Avec système d'audit
- [ ] Avec observations
- [ ] Avec notifications
- [ ] Avec permissions

### Performance
- [ ] Temps de chargement acceptables
- [ ] Pagination efficace
- [ ] Pas de fuites mémoire

### Sécurité
- [ ] Contrôle d'accès strict
- [ ] Validation des données
- [ ] Protection contre injections

## Rapport de test

À compléter après exécution des tests :

### Résultats
- Tests réussis : __ / __
- Tests échoués : __ / __
- Bugs critiques : __
- Bugs mineurs : __

### Recommandations
1. [À compléter]
2. [À compléter]
3. [À compléter]

### Prochaines étapes
1. [À compléter]
2. [À compléter]
3. [À compléter]
