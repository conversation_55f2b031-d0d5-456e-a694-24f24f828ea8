import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ActionService } from "@/lib/services/action-service"
import { updateActionSchema, assignActionSchema, completeActionSchema } from "@/lib/validations/action"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"
import { z } from "zod"

/**
 * GET /api/actions/[id] - Obtenir une action par ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'actions', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      const action = await ActionService.getActionById(params.id)

      if (!action) {
        return NextResponse.json(
          { success: false, error: "Action non trouvée" },
          { status: 404 }
        )
      }

      // Vérifier si l'utilisateur peut voir cette action
      if (user.role === "AUDITOR" && action.assigneeId !== user.id) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      return NextResponse.json({
        success: true,
        data: action
      })

    } catch (error) {
      console.error("Erreur lors de la récupération de l'action:", error)
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * PUT /api/actions/[id] - Mettre à jour une action
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'actions', 'update')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que l'action existe
      const existingAction = await ActionService.getActionById(params.id)
      if (!existingAction) {
        return NextResponse.json(
          { success: false, error: "Action non trouvée" },
          { status: 404 }
        )
      }

      // Vérifier si l'utilisateur peut modifier cette action
      if (user.role === "AUDITOR" && existingAction.assigneeId !== user.id) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      const body = await req.json()
      const validatedData = updateActionSchema.parse(body)

      const action = await ActionService.updateAction(params.id, validatedData)

      return NextResponse.json({
        success: true,
        data: action
      })

    } catch (error) {
      console.error("Erreur lors de la mise à jour de l'action:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Données invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * DELETE /api/actions/[id] - Supprimer une action
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'actions', 'delete')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que l'action existe
      const existingAction = await ActionService.getActionById(params.id)
      if (!existingAction) {
        return NextResponse.json(
          { success: false, error: "Action non trouvée" },
          { status: 404 }
        )
      }

      await ActionService.deleteAction(params.id)

      return NextResponse.json({
        success: true,
        message: "Action supprimée avec succès"
      })

    } catch (error) {
      console.error("Erreur lors de la suppression de l'action:", error)
      
      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
