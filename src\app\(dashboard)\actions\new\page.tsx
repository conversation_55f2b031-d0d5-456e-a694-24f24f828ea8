"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ActionForm } from "@/components/features/actions/action-form"
import { useActionActions } from "@/hooks/use-action-actions"
import { useFormData } from "@/hooks/use-form-data"
import { useAudits } from "@/hooks/use-audits"
import { useObservations } from "@/hooks/use-observations"
import { CreateActionInput } from "@/lib/validations/action"
import { ArrowLeft, AlertCircle, Loader2 } from "lucide-react"
import { useRouter, useSearchParams } from "next/navigation"

export default function NewActionPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // Récupérer les paramètres optionnels de l'URL
  const auditId = searchParams.get('auditId')
  const observationId = searchParams.get('observationId')

  const {
    loading: actionLoading,
    error: actionError,
    createAction,
    clearError
  } = useActionActions()

  const {
    users,
    loading: usersLoading,
    error: usersError
  } = useFormData()

  // Récupérer les audits pour le sélecteur
  const {
    audits,
    loading: auditsLoading,
    error: auditsError
  } = useAudits({
    initialFilters: { limit: 100 }, // Récupérer plus d'audits pour le sélecteur
    autoFetch: true
  })

  // Récupérer les observations si un audit est spécifié
  const {
    observations,
    loading: observationsLoading,
    error: observationsError
  } = useObservations({
    auditId: auditId || undefined,
    initialFilters: { limit: 100 },
    autoFetch: !!auditId
  })

  const handleSubmit = async (data: CreateActionInput) => {
    const action = await createAction(data)
    if (action) {
      router.push(`/actions/${action.id}`)
    }
  }

  const dataLoading = usersLoading || auditsLoading || (auditId && observationsLoading)
  const dataError = usersError || auditsError || observationsError

  if (dataLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" disabled>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold magneto-title">Nouvelle action</h1>
            <p className="text-gray-600">Chargement...</p>
          </div>
        </div>

        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (dataError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold magneto-title">Nouvelle action</h1>
            <p className="text-gray-600">Erreur lors du chargement</p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{dataError}</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Retour
        </Button>

        <div>
          <h1 className="text-3xl font-bold magneto-title">Nouvelle action</h1>
          <p className="text-gray-600">
            Créez une nouvelle action corrective
          </p>
        </div>
      </div>

      {/* Informations contextuelles */}
      {(auditId || observationId) && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {auditId && !observationId && "Cette action sera créée pour l'audit sélectionné."}
            {observationId && "Cette action sera créée pour l'observation sélectionnée."}
          </AlertDescription>
        </Alert>
      )}

      {/* Formulaire */}
      <ActionForm
        users={users}
        audits={audits.audits} // Extraire le tableau d'audits de la réponse paginée
        observations={observations}
        onSubmit={handleSubmit}
        loading={actionLoading}
        error={actionError}
        defaultAuditId={auditId || undefined}
        defaultObservationId={observationId || undefined}
      />
    </div>
  )
}
