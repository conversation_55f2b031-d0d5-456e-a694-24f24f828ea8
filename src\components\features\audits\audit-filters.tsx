"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { AuditFilters, AuditStatus } from "@/lib/validations/audit"
import { Search, Filter, X } from "lucide-react"

interface AuditFiltersProps {
  filters: AuditFilters
  onFiltersChange: (filters: AuditFilters) => void
  organizations?: Array<{ id: string; name: string }>
  auditors?: Array<{ id: string; name: string; email: string }>
  loading?: boolean
}

export function AuditFiltersComponent({ 
  filters, 
  onFiltersChange, 
  organizations = [],
  auditors = [],
  loading = false 
}: AuditFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const updateFilter = (key: keyof AuditFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
      page: 1 // Reset to first page when filtering
    })
  }

  const clearFilters = () => {
    onFiltersChange({
      page: 1,
      limit: filters.limit,
      sortBy: "createdAt",
      sortOrder: "desc"
    })
  }

  const hasActiveFilters = !!(
    filters.search ||
    filters.status ||
    filters.organizationId ||
    filters.auditorId ||
    filters.startDateFrom ||
    filters.startDateTo
  )

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtres
          </CardTitle>
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
                className="text-red-600 hover:text-red-700"
              >
                <X className="h-4 w-4 mr-1" />
                Effacer
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? "Réduire" : "Étendre"}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Recherche - toujours visible */}
        <div className="space-y-2">
          <Label htmlFor="search">Recherche</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="search"
              placeholder="Rechercher par titre ou description..."
              value={filters.search || ""}
              onChange={(e) => updateFilter("search", e.target.value || undefined)}
              className="pl-10"
              disabled={loading}
            />
          </div>
        </div>

        {/* Filtres étendus */}
        {isExpanded && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Statut */}
            <div className="space-y-2">
              <Label>Statut</Label>
              <Select
                value={filters.status || "all"}
                onValueChange={(value) => updateFilter("status", value === "all" ? undefined : value)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tous les statuts" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les statuts</SelectItem>
                  <SelectItem value={AuditStatus.PLANNED}>Planifié</SelectItem>
                  <SelectItem value={AuditStatus.IN_PROGRESS}>En cours</SelectItem>
                  <SelectItem value={AuditStatus.COMPLETED}>Terminé</SelectItem>
                  <SelectItem value={AuditStatus.ON_HOLD}>En attente</SelectItem>
                  <SelectItem value={AuditStatus.CANCELLED}>Annulé</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Organisation */}
            {organizations.length > 0 && (
              <div className="space-y-2">
                <Label>Organisation</Label>
                <Select
                  value={filters.organizationId || "all"}
                  onValueChange={(value) => updateFilter("organizationId", value === "all" ? undefined : value)}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Toutes les organisations" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes les organisations</SelectItem>
                    {organizations.map((org) => (
                      <SelectItem key={org.id} value={org.id}>
                        {org.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Auditeur */}
            {auditors.length > 0 && (
              <div className="space-y-2">
                <Label>Auditeur</Label>
                <Select
                  value={filters.auditorId || "all"}
                  onValueChange={(value) => updateFilter("auditorId", value === "all" ? undefined : value)}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Tous les auditeurs" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les auditeurs</SelectItem>
                    {auditors.map((auditor) => (
                      <SelectItem key={auditor.id} value={auditor.id}>
                        {auditor.name || auditor.email}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Date de début - De */}
            <div className="space-y-2">
              <Label htmlFor="startDateFrom">Date de début - De</Label>
              <Input
                id="startDateFrom"
                type="date"
                value={filters.startDateFrom ? filters.startDateFrom.split('T')[0] : ""}
                onChange={(e) => updateFilter("startDateFrom", e.target.value ? `${e.target.value}T00:00:00.000Z` : undefined)}
                disabled={loading}
              />
            </div>

            {/* Date de début - À */}
            <div className="space-y-2">
              <Label htmlFor="startDateTo">Date de début - À</Label>
              <Input
                id="startDateTo"
                type="date"
                value={filters.startDateTo ? filters.startDateTo.split('T')[0] : ""}
                onChange={(e) => updateFilter("startDateTo", e.target.value ? `${e.target.value}T23:59:59.999Z` : undefined)}
                disabled={loading}
              />
            </div>

            {/* Tri */}
            <div className="space-y-2">
              <Label>Trier par</Label>
              <div className="flex gap-2">
                <Select
                  value={filters.sortBy}
                  onValueChange={(value) => updateFilter("sortBy", value)}
                  disabled={loading}
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="title">Titre</SelectItem>
                    <SelectItem value="startDate">Date de début</SelectItem>
                    <SelectItem value="status">Statut</SelectItem>
                    <SelectItem value="createdAt">Date de création</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select
                  value={filters.sortOrder}
                  onValueChange={(value) => updateFilter("sortOrder", value)}
                  disabled={loading}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">Croissant</SelectItem>
                    <SelectItem value="desc">Décroissant</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
