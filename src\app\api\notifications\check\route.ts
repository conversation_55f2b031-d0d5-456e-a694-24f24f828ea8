import { NextRequest, NextResponse } from "next/server"
import { NotificationService } from "@/lib/services/notification-service"

/**
 * POST /api/notifications/check - Déclencher les vérifications de notifications
 * Cette route peut être appelée par un cron job ou un système de tâches planifiées
 */
export async function POST(request: NextRequest) {
  try {
    // Vérifier si la requête provient d'un système autorisé
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET || 'default-secret'
    
    if (authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { success: false, error: "Non autorisé" },
        { status: 401 }
      )
    }

    console.log('Démarrage des vérifications de notifications...')
    
    // Exécuter toutes les vérifications
    await NotificationService.runNotificationChecks()
    
    // Nettoyer les anciennes notifications
    await NotificationService.cleanupOldNotifications()
    
    console.log('Vérifications de notifications terminées avec succès')

    return NextResponse.json({
      success: true,
      message: "Vérifications de notifications exécutées avec succès",
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Erreur lors des vérifications de notifications:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: "Erreur lors des vérifications de notifications",
        details: error instanceof Error ? error.message : "Erreur inconnue"
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/notifications/check - Obtenir le statut des vérifications
 */
export async function GET(request: NextRequest) {
  try {
    // Cette route peut être utilisée pour vérifier que le système fonctionne
    return NextResponse.json({
      success: true,
      message: "Service de notifications opérationnel",
      timestamp: new Date().toISOString(),
      endpoints: {
        check: "POST /api/notifications/check - Déclencher les vérifications",
        status: "GET /api/notifications/check - Obtenir le statut"
      }
    })

  } catch (error) {
    console.error('Erreur lors de la vérification du statut:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: "Erreur lors de la vérification du statut"
      },
      { status: 500 }
    )
  }
}
