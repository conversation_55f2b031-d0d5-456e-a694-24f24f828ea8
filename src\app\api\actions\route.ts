import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ActionService } from "@/lib/services/action-service"
import { createActionSchema, actionFiltersSchema } from "@/lib/validations/action"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"
import { z } from "zod"

/**
 * GET /api/actions - Obtenir la liste des actions
 */
export async function GET(request: NextRequest) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'actions', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Parser les paramètres de requête
      const { searchParams } = new URL(req.url)
      const filters = actionFiltersSchema.parse({
        status: searchParams.get('status') || undefined,
        priority: searchParams.get('priority') || undefined,
        auditId: searchParams.get('auditId') || undefined,
        observationId: searchParams.get('observationId') || undefined,
        assigneeId: searchParams.get('assigneeId') || undefined,
        creatorId: searchParams.get('creatorId') || undefined,
        dueDateFrom: searchParams.get('dueDateFrom') || undefined,
        dueDateTo: searchParams.get('dueDateTo') || undefined,
        search: searchParams.get('search') || undefined,
        page: searchParams.get('page') || 1,
        limit: searchParams.get('limit') || 10,
        sortBy: searchParams.get('sortBy') || 'createdAt',
        sortOrder: searchParams.get('sortOrder') || 'desc',
        includeOverdue: searchParams.get('includeOverdue') === 'true'
      })

      // Si l'utilisateur n'est pas admin, filtrer par son organisation
      if (user.role !== "SUPER_ADMIN" && user.organizationId) {
        // Pour les actions, on filtre via les audits de l'organisation
        // Cette logique sera implémentée dans le service si nécessaire
      }

      // Si l'utilisateur est un auditeur simple, ne voir que ses actions assignées
      if (user.role === "AUDITOR") {
        filters.assigneeId = user.id
      }

      const result = await ActionService.getActions(filters)

      return NextResponse.json({
        success: true,
        data: result
      })

    } catch (error) {
      console.error("Erreur lors de la récupération des actions:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Paramètres invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * POST /api/actions - Créer une nouvelle action
 */
export async function POST(request: NextRequest) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'actions', 'create')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      const body = await req.json()
      const validatedData = createActionSchema.parse(body)

      const action = await ActionService.createAction(validatedData, user.id)

      return NextResponse.json({
        success: true,
        data: action
      }, { status: 201 })

    } catch (error) {
      console.error("Erreur lors de la création de l'action:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Données invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
