"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { useNotifications, useNotificationChecks } from "@/hooks/use-notifications"
import { useSession } from "@/lib/auth/client"
import { NotificationData } from "@/lib/services/notification-service"
import { Bell, CheckCircle, Clock, AlertTriangle, User, Eye, RefreshCw, CheckCheck } from "lucide-react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { useRouter } from "next/navigation"

export default function NotificationsPage() {
  const router = useRouter()
  const { data: session } = useSession()
  const user = session?.user
  
  const [activeTab, setActiveTab] = useState("all")

  const {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    refetch
  } = useNotifications(user?.id || "")

  const {
    triggerChecks,
    loading: checksLoading,
    error: checksError
  } = useNotificationChecks()

  const getNotificationIcon = (type: NotificationData['type']) => {
    switch (type) {
      case 'ACTION_OVERDUE':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      case 'ACTION_DUE_SOON':
        return <Clock className="h-5 w-5 text-orange-500" />
      case 'ACTION_ASSIGNED':
        return <User className="h-5 w-5 text-blue-500" />
      case 'ACTION_COMPLETED':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      default:
        return <Bell className="h-5 w-5 text-gray-500" />
    }
  }

  const getNotificationColor = (type: NotificationData['type']) => {
    switch (type) {
      case 'ACTION_OVERDUE':
        return "border-l-red-500 bg-red-50"
      case 'ACTION_DUE_SOON':
        return "border-l-orange-500 bg-orange-50"
      case 'ACTION_ASSIGNED':
        return "border-l-blue-500 bg-blue-50"
      case 'ACTION_COMPLETED':
        return "border-l-green-500 bg-green-50"
      default:
        return "border-l-gray-500 bg-gray-50"
    }
  }

  const handleNotificationClick = async (notification: NotificationData) => {
    if (!notification.read) {
      try {
        await markAsRead(notification.id)
      } catch (error) {
        console.error('Erreur lors du marquage de la notification:', error)
      }
    }
    router.push(`/actions/${notification.actionId}`)
  }

  const handleTriggerChecks = async () => {
    const success = await triggerChecks()
    if (success) {
      await refetch()
    }
  }

  const formatNotificationDate = (date: Date) => {
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      return "À l'instant"
    } else if (diffInHours < 24) {
      return `Il y a ${diffInHours}h`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      if (diffInDays === 1) {
        return "Hier"
      } else if (diffInDays < 7) {
        return `Il y a ${diffInDays}j`
      } else {
        return format(date, "dd/MM/yyyy à HH:mm", { locale: fr })
      }
    }
  }

  const filteredNotifications = notifications.filter(notification => {
    switch (activeTab) {
      case "unread":
        return !notification.read
      case "read":
        return notification.read
      default:
        return true
    }
  })

  if (!user) {
    return (
      <div className="space-y-6">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Vous devez être connecté pour voir vos notifications.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold magneto-title">Notifications</h1>
          <p className="text-gray-600">
            Gérez vos notifications et alertes
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => refetch()}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Actualiser
          </Button>
          
          {unreadCount > 0 && (
            <Button
              variant="outline"
              onClick={markAllAsRead}
              disabled={loading}
            >
              <CheckCheck className="h-4 w-4 mr-2" />
              Tout marquer comme lu
            </Button>
          )}
          
          <Button
            onClick={handleTriggerChecks}
            disabled={checksLoading}
            className="magneto-button"
          >
            <Bell className={`h-4 w-4 mr-2 ${checksLoading ? 'animate-pulse' : ''}`} />
            Vérifier les alertes
          </Button>
        </div>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{notifications.length}</div>
            <p className="text-xs text-muted-foreground">Notifications</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Non lues</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{unreadCount}</div>
            <p className="text-xs text-muted-foreground">À traiter</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Lues</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{notifications.length - unreadCount}</div>
            <p className="text-xs text-muted-foreground">Traitées</p>
          </CardContent>
        </Card>
      </div>

      {/* Erreurs */}
      {(error || checksError) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error || checksError}
          </AlertDescription>
        </Alert>
      )}

      {/* Liste des notifications */}
      <Card>
        <CardHeader>
          <CardTitle>Mes notifications</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">
                Toutes ({notifications.length})
              </TabsTrigger>
              <TabsTrigger value="unread">
                Non lues ({unreadCount})
              </TabsTrigger>
              <TabsTrigger value="read">
                Lues ({notifications.length - unreadCount})
              </TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-6">
              {loading ? (
                <div className="space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-20 bg-gray-100 rounded animate-pulse" />
                  ))}
                </div>
              ) : filteredNotifications.length === 0 ? (
                <div className="text-center py-12">
                  <Bell className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    {activeTab === "unread" ? "Aucune notification non lue" : 
                     activeTab === "read" ? "Aucune notification lue" : 
                     "Aucune notification"}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {activeTab === "unread" ? "Toutes vos notifications sont à jour." : 
                     "Les notifications apparaîtront ici."}
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 border-l-4 rounded-r-lg cursor-pointer transition-colors hover:bg-gray-50 ${getNotificationColor(notification.type)} ${
                        !notification.read ? 'bg-opacity-100' : 'bg-opacity-50'
                      }`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-0.5">
                          {getNotificationIcon(notification.type)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className={`text-sm font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-600'}`}>
                              {notification.title}
                            </p>
                            <div className="flex items-center gap-2">
                              <span className="text-xs text-gray-400">
                                {formatNotificationDate(notification.createdAt)}
                              </span>
                              {!notification.read && (
                                <div className="w-2 h-2 bg-blue-500 rounded-full" />
                              )}
                            </div>
                          </div>
                          
                          <p className={`text-sm mt-1 ${!notification.read ? 'text-gray-700' : 'text-gray-500'}`}>
                            {notification.message}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
