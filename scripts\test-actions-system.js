#!/usr/bin/env node

/**
 * Script de test automatisé pour le système de gestion des actions
 * 
 * Usage: node scripts/test-actions-system.js
 * 
 * Ce script teste les fonctionnalités principales du système d'actions :
 * - Création d'actions
 * - Gestion des statuts
 * - Notifications
 * - Permissions
 */

const fs = require('fs')
const path = require('path')

// Configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000'
const TEST_USER_EMAIL = process.env.TEST_USER_EMAIL || '<EMAIL>'
const TEST_USER_PASSWORD = process.env.TEST_USER_PASSWORD || 'password123'

// Couleurs pour les logs
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green')
}

function logError(message) {
  log(`❌ ${message}`, 'red')
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow')
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue')
}

// Tests de validation des fichiers
function testFileStructure() {
  logInfo('Test de la structure des fichiers...')
  
  const requiredFiles = [
    'src/lib/validations/action.ts',
    'src/lib/services/action-service.ts',
    'src/hooks/use-action-actions.ts',
    'src/hooks/use-actions.ts',
    'src/components/features/actions/action-form.tsx',
    'src/components/features/actions/action-table.tsx',
    'src/app/(dashboard)/actions/page.tsx',
    'src/app/(dashboard)/actions/new/page.tsx',
    'src/app/api/actions/route.ts'
  ]

  let allFilesExist = true

  requiredFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file)
    if (fs.existsSync(filePath)) {
      logSuccess(`Fichier trouvé: ${file}`)
    } else {
      logError(`Fichier manquant: ${file}`)
      allFilesExist = false
    }
  })

  return allFilesExist
}

// Test de validation des types TypeScript
function testTypeDefinitions() {
  logInfo('Test des définitions de types...')
  
  try {
    const actionValidationPath = path.join(process.cwd(), 'src/lib/validations/action.ts')
    const content = fs.readFileSync(actionValidationPath, 'utf8')
    
    const requiredExports = [
      'ActionStatus',
      'ActionPriority',
      'createActionSchema',
      'updateActionSchema',
      'ActionWithRelations'
    ]

    let allExportsFound = true

    requiredExports.forEach(exportName => {
      if (content.includes(`export enum ${exportName}`) || 
          content.includes(`export const ${exportName}`) ||
          content.includes(`export interface ${exportName}`) ||
          content.includes(`export type ${exportName}`)) {
        logSuccess(`Export trouvé: ${exportName}`)
      } else {
        logError(`Export manquant: ${exportName}`)
        allExportsFound = false
      }
    })

    return allExportsFound
  } catch (error) {
    logError(`Erreur lors de la lecture du fichier de validation: ${error.message}`)
    return false
  }
}

// Test de validation des services
function testServiceMethods() {
  logInfo('Test des méthodes de service...')
  
  try {
    const servicePath = path.join(process.cwd(), 'src/lib/services/action-service.ts')
    const content = fs.readFileSync(servicePath, 'utf8')
    
    const requiredMethods = [
      'createAction',
      'getActionById',
      'getActions',
      'updateAction',
      'deleteAction',
      'assignAction',
      'completeAction',
      'getActionStats'
    ]

    let allMethodsFound = true

    requiredMethods.forEach(method => {
      if (content.includes(`static async ${method}`) || content.includes(`async ${method}`)) {
        logSuccess(`Méthode trouvée: ${method}`)
      } else {
        logError(`Méthode manquante: ${method}`)
        allMethodsFound = false
      }
    })

    return allMethodsFound
  } catch (error) {
    logError(`Erreur lors de la lecture du fichier de service: ${error.message}`)
    return false
  }
}

// Test de validation des hooks
function testHooks() {
  logInfo('Test des hooks React...')
  
  const hooks = [
    {
      file: 'src/hooks/use-action-actions.ts',
      exports: ['useActionActions']
    },
    {
      file: 'src/hooks/use-actions.ts',
      exports: ['useActions', 'useAuditActions', 'useActionStats']
    },
    {
      file: 'src/hooks/use-action-permissions.ts',
      exports: ['useActionPermissions']
    }
  ]

  let allHooksValid = true

  hooks.forEach(hook => {
    try {
      const hookPath = path.join(process.cwd(), hook.file)
      const content = fs.readFileSync(hookPath, 'utf8')
      
      hook.exports.forEach(exportName => {
        if (content.includes(`export function ${exportName}`) || 
            content.includes(`export const ${exportName}`)) {
          logSuccess(`Hook trouvé: ${exportName}`)
        } else {
          logError(`Hook manquant: ${exportName}`)
          allHooksValid = false
        }
      })
    } catch (error) {
      logError(`Erreur lors de la lecture du hook ${hook.file}: ${error.message}`)
      allHooksValid = false
    }
  })

  return allHooksValid
}

// Test de validation des composants
function testComponents() {
  logInfo('Test des composants React...')
  
  const components = [
    'src/components/features/actions/action-form.tsx',
    'src/components/features/actions/action-table.tsx',
    'src/components/features/actions/action-card.tsx',
    'src/components/features/actions/action-status-badge.tsx',
    'src/components/features/actions/action-priority-badge.tsx'
  ]

  let allComponentsValid = true

  components.forEach(component => {
    try {
      const componentPath = path.join(process.cwd(), component)
      const content = fs.readFileSync(componentPath, 'utf8')
      
      if (content.includes('export function') || content.includes('export default')) {
        logSuccess(`Composant valide: ${path.basename(component)}`)
      } else {
        logError(`Composant invalide: ${path.basename(component)}`)
        allComponentsValid = false
      }
    } catch (error) {
      logError(`Erreur lors de la lecture du composant ${component}: ${error.message}`)
      allComponentsValid = false
    }
  })

  return allComponentsValid
}

// Test de validation des pages
function testPages() {
  logInfo('Test des pages Next.js...')
  
  const pages = [
    'src/app/(dashboard)/actions/page.tsx',
    'src/app/(dashboard)/actions/new/page.tsx',
    'src/app/(dashboard)/actions/[id]/page.tsx',
    'src/app/(dashboard)/actions/[id]/edit/page.tsx'
  ]

  let allPagesValid = true

  pages.forEach(page => {
    try {
      const pagePath = path.join(process.cwd(), page)
      const content = fs.readFileSync(pagePath, 'utf8')
      
      if (content.includes('export default function') && content.includes('Page')) {
        logSuccess(`Page valide: ${path.basename(page)}`)
      } else {
        logError(`Page invalide: ${path.basename(page)}`)
        allPagesValid = false
      }
    } catch (error) {
      logError(`Erreur lors de la lecture de la page ${page}: ${error.message}`)
      allPagesValid = false
    }
  })

  return allPagesValid
}

// Test de validation des API routes
function testApiRoutes() {
  logInfo('Test des routes API...')
  
  const routes = [
    'src/app/api/actions/route.ts',
    'src/app/api/actions/[id]/route.ts',
    'src/app/api/actions/[id]/assign/route.ts',
    'src/app/api/actions/[id]/complete/route.ts',
    'src/app/api/audits/[id]/actions/route.ts'
  ]

  let allRoutesValid = true

  routes.forEach(route => {
    try {
      const routePath = path.join(process.cwd(), route)
      const content = fs.readFileSync(routePath, 'utf8')
      
      const hasGetOrPost = content.includes('export async function GET') || 
                          content.includes('export async function POST') ||
                          content.includes('export async function PUT') ||
                          content.includes('export async function DELETE')
      
      if (hasGetOrPost) {
        logSuccess(`Route API valide: ${path.basename(route)}`)
      } else {
        logError(`Route API invalide: ${path.basename(route)}`)
        allRoutesValid = false
      }
    } catch (error) {
      logError(`Erreur lors de la lecture de la route ${route}: ${error.message}`)
      allRoutesValid = false
    }
  })

  return allRoutesValid
}

// Test de validation des notifications
function testNotificationSystem() {
  logInfo('Test du système de notifications...')
  
  const notificationFiles = [
    'src/lib/services/notification-service.ts',
    'src/components/features/notifications/notification-bell.tsx',
    'src/components/features/notifications/due-actions-dashboard.tsx',
    'src/hooks/use-notifications.ts'
  ]

  let allNotificationFilesValid = true

  notificationFiles.forEach(file => {
    try {
      const filePath = path.join(process.cwd(), file)
      if (fs.existsSync(filePath)) {
        logSuccess(`Fichier de notification trouvé: ${path.basename(file)}`)
      } else {
        logError(`Fichier de notification manquant: ${path.basename(file)}`)
        allNotificationFilesValid = false
      }
    } catch (error) {
      logError(`Erreur lors de la vérification du fichier ${file}: ${error.message}`)
      allNotificationFilesValid = false
    }
  })

  return allNotificationFilesValid
}

// Fonction principale de test
async function runTests() {
  log('🚀 Démarrage des tests du système d\'actions', 'blue')
  log('=' * 50, 'blue')
  
  const testResults = {
    fileStructure: testFileStructure(),
    typeDefinitions: testTypeDefinitions(),
    serviceMethods: testServiceMethods(),
    hooks: testHooks(),
    components: testComponents(),
    pages: testPages(),
    apiRoutes: testApiRoutes(),
    notificationSystem: testNotificationSystem()
  }

  log('\n📊 Résumé des tests:', 'blue')
  log('=' * 30, 'blue')

  let totalTests = 0
  let passedTests = 0

  Object.entries(testResults).forEach(([testName, result]) => {
    totalTests++
    if (result) {
      passedTests++
      logSuccess(`${testName}: RÉUSSI`)
    } else {
      logError(`${testName}: ÉCHOUÉ`)
    }
  })

  log(`\n📈 Score final: ${passedTests}/${totalTests} tests réussis`, 
      passedTests === totalTests ? 'green' : 'yellow')

  if (passedTests === totalTests) {
    logSuccess('🎉 Tous les tests sont passés ! Le système d\'actions est prêt.')
  } else {
    logWarning('⚠️  Certains tests ont échoué. Veuillez corriger les problèmes avant de continuer.')
  }

  // Générer un rapport de test
  const report = {
    timestamp: new Date().toISOString(),
    totalTests,
    passedTests,
    failedTests: totalTests - passedTests,
    results: testResults,
    recommendations: generateRecommendations(testResults)
  }

  const reportPath = path.join(process.cwd(), 'test-results.json')
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  logInfo(`📄 Rapport de test sauvegardé dans: ${reportPath}`)

  return passedTests === totalTests
}

function generateRecommendations(testResults) {
  const recommendations = []

  if (!testResults.fileStructure) {
    recommendations.push('Vérifier que tous les fichiers requis sont présents')
  }

  if (!testResults.typeDefinitions) {
    recommendations.push('Corriger les définitions de types TypeScript')
  }

  if (!testResults.serviceMethods) {
    recommendations.push('Implémenter toutes les méthodes requises dans ActionService')
  }

  if (!testResults.hooks) {
    recommendations.push('Vérifier l\'implémentation des hooks React')
  }

  if (!testResults.components) {
    recommendations.push('Corriger les composants React défaillants')
  }

  if (!testResults.pages) {
    recommendations.push('Vérifier l\'implémentation des pages Next.js')
  }

  if (!testResults.apiRoutes) {
    recommendations.push('Corriger les routes API manquantes ou défaillantes')
  }

  if (!testResults.notificationSystem) {
    recommendations.push('Compléter l\'implémentation du système de notifications')
  }

  if (recommendations.length === 0) {
    recommendations.push('Système prêt pour les tests manuels et la mise en production')
  }

  return recommendations
}

// Exécution du script
if (require.main === module) {
  runTests().catch(error => {
    logError(`Erreur lors de l'exécution des tests: ${error.message}`)
    process.exit(1)
  })
}

module.exports = { runTests }
