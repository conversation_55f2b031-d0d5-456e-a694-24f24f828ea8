"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { RoleSelector } from "./role-selector"
import { createUserSchema, updateUserSchema, CreateUserData, UpdateUserData, UserRole } from "@/lib/validations/user"
import { Eye, EyeOff, AlertCircle } from "lucide-react"

interface Organization {
  id: string
  name: string
}

interface UserFormProps {
  mode: "create" | "edit"
  initialData?: Partial<UpdateUserData>
  organizations?: Organization[]
  onSubmit: (data: CreateUserData | UpdateUserData) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
  className?: string
}

export function UserForm({
  mode,
  initialData,
  organizations = [],
  onSubmit,
  onCancel,
  isLoading = false,
  className
}: UserFormProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)

  const schema = mode === "create" ? createUserSchema : updateUserSchema
  
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<CreateUserData | UpdateUserData>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: initialData?.name || "",
      email: initialData?.email || "",
      role: initialData?.role || "USER",
      organizationId: initialData?.organizationId || undefined,
      isActive: initialData?.isActive ?? true,
      ...(mode === "create" && { password: "" })
    }
  })

  const watchedRole = watch("role")
  const watchedIsActive = watch("isActive")

  const handleFormSubmit = async (data: CreateUserData | UpdateUserData) => {
    try {
      setSubmitError(null)
      await onSubmit(data)
    } catch (error: any) {
      setSubmitError(error.message || "Une erreur est survenue")
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>
          {mode === "create" ? "Créer un utilisateur" : "Modifier l'utilisateur"}
        </CardTitle>
        <CardDescription>
          {mode === "create" 
            ? "Remplissez les informations pour créer un nouvel utilisateur"
            : "Modifiez les informations de l'utilisateur"
          }
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {submitError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* Nom */}
          <div className="space-y-2">
            <Label htmlFor="name">Nom complet *</Label>
            <Input
              id="name"
              {...register("name")}
              placeholder="Entrez le nom complet"
              className="magneto-input"
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          {/* Email */}
          <div className="space-y-2">
            <Label htmlFor="email">Adresse email *</Label>
            <Input
              id="email"
              type="email"
              {...register("email")}
              placeholder="<EMAIL>"
              className="magneto-input"
            />
            {errors.email && (
              <p className="text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          {/* Mot de passe (seulement en mode création) */}
          {mode === "create" && (
            <div className="space-y-2">
              <Label htmlFor="password">Mot de passe *</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  {...register("password")}
                  placeholder="Entrez un mot de passe sécurisé"
                  className="magneto-input pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-600">{errors.password.message}</p>
              )}
              <p className="text-xs text-gray-500">
                Le mot de passe doit contenir au moins 8 caractères avec une majuscule, une minuscule et un chiffre
              </p>
            </div>
          )}

          {/* Rôle */}
          <div className="space-y-2">
            <Label htmlFor="role">Rôle *</Label>
            <RoleSelector
              value={watchedRole as UserRole}
              onValueChange={(value) => setValue("role", value)}
              className="magneto-input"
            />
            {errors.role && (
              <p className="text-sm text-red-600">{errors.role.message}</p>
            )}
          </div>

          {/* Organisation */}
          <div className="space-y-2">
            <Label htmlFor="organizationId">Organisation</Label>
            <Select
              value={watch("organizationId") || "none"}
              onValueChange={(value) => setValue("organizationId", value === "none" ? undefined : value)}
            >
              <SelectTrigger className="magneto-input">
                <SelectValue placeholder="Sélectionner une organisation (optionnel)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Aucune organisation</SelectItem>
                {organizations.map((org) => (
                  <SelectItem key={org.id} value={org.id}>
                    {org.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.organizationId && (
              <p className="text-sm text-red-600">{errors.organizationId.message}</p>
            )}
          </div>

          {/* Statut actif */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="isActive">Utilisateur actif</Label>
              <p className="text-sm text-gray-500">
                Les utilisateurs inactifs ne peuvent pas se connecter
              </p>
            </div>
            <Switch
              id="isActive"
              checked={watchedIsActive}
              onCheckedChange={(checked) => setValue("isActive", checked)}
            />
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="magneto-button flex-1"
            >
              {isSubmitting || isLoading ? "En cours..." : (
                mode === "create" ? "Créer l'utilisateur" : "Mettre à jour"
              )}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting || isLoading}
                className="flex-1"
              >
                Annuler
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
