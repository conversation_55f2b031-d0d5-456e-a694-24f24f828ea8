"use client"

import { useState, useEffect, useCallback } from "react"
import { ActionWithRelations, ActionFilters, ActionStats } from "@/lib/validations/action"

interface UseActionsResult {
  actions: ActionWithRelations[]
  loading: boolean
  error: string | null
  total: number
  page: number
  limit: number
  totalPages: number
  refetch: () => Promise<void>
  setFilters: (filters: Partial<ActionFilters>) => void
  filters: ActionFilters
}

interface UseActionsOptions {
  auditId?: string
  observationId?: string
  assigneeId?: string
  initialFilters?: Partial<ActionFilters>
  autoFetch?: boolean
}

export function useActions(options: UseActionsOptions = {}): UseActionsResult {
  const {
    auditId,
    observationId,
    assigneeId,
    initialFilters = {},
    autoFetch = true
  } = options

  const [actions, setActions] = useState<ActionWithRelations[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [total, setTotal] = useState(0)
  const [page, setPage] = useState(1)
  const [limit, setLimit] = useState(10)
  const [totalPages, setTotalPages] = useState(0)

  const [filters, setFiltersState] = useState<ActionFilters>({
    page: 1,
    limit: 10,
    sortBy: "createdAt",
    sortOrder: "desc",
    includeOverdue: true,
    ...initialFilters,
    ...(auditId && { auditId }),
    ...(observationId && { observationId }),
    ...(assigneeId && { assigneeId })
  })

  const setFilters = useCallback((newFilters: Partial<ActionFilters>) => {
    setFiltersState(prev => ({
      ...prev,
      ...newFilters,
      page: newFilters.page || 1 // Reset page when filters change
    }))
  }, [])

  const fetchActions = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Construire l'URL avec les paramètres
      const searchParams = new URLSearchParams()
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString())
        }
      })

      const response = await fetch(`/api/actions?${searchParams.toString()}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la récupération des actions")
      }

      if (result.success) {
        setActions(result.data.actions)
        setTotal(result.data.total)
        setPage(result.data.page)
        setLimit(result.data.limit)
        setTotalPages(result.data.totalPages)
      } else {
        throw new Error(result.error || "Erreur lors de la récupération des actions")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la récupération des actions:", err)
      setActions([])
      setTotal(0)
      setTotalPages(0)
    } finally {
      setLoading(false)
    }
  }, [filters])

  // Auto-fetch when filters change
  useEffect(() => {
    if (autoFetch) {
      fetchActions()
    }
  }, [fetchActions, autoFetch])

  return {
    actions,
    loading,
    error,
    total,
    page,
    limit,
    totalPages,
    refetch: fetchActions,
    setFilters,
    filters
  }
}

// Hook spécialisé pour les actions d'un audit
export function useAuditActions(auditId: string) {
  return useActions({ auditId })
}

// Hook spécialisé pour les actions d'une observation
export function useObservationActions(observationId: string) {
  return useActions({ observationId })
}

// Hook spécialisé pour les actions assignées à un utilisateur
export function useUserActions(assigneeId: string) {
  return useActions({ assigneeId })
}

// Hook pour les statistiques des actions
export function useActionStats(auditId?: string) {
  const [stats, setStats] = useState<ActionStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const searchParams = new URLSearchParams()
      if (auditId) {
        searchParams.append('auditId', auditId)
      }

      const response = await fetch(`/api/actions/stats?${searchParams.toString()}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la récupération des statistiques")
      }

      if (result.success) {
        setStats(result.data)
      } else {
        throw new Error(result.error || "Erreur lors de la récupération des statistiques")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la récupération des statistiques:", err)
      setStats(null)
    } finally {
      setLoading(false)
    }
  }, [auditId])

  useEffect(() => {
    fetchStats()
  }, [fetchStats])

  return {
    stats,
    loading,
    error,
    refetch: fetchStats
  }
}
