"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  CreateActionInput, 
  UpdateActionInput, 
  createActionSchema, 
  updateActionSchema,
  ActionWithRelations,
  ActionStatus,
  ActionPriority
} from "@/lib/validations/action"
import { Calendar, Users, AlertCircle, Loader2, FileText } from "lucide-react"
import { format } from "date-fns"

interface User {
  id: string
  name: string | null
  email: string
  role: string
}

interface Audit {
  id: string
  title: string
  status: string
}

interface Observation {
  id: string
  title: string
  severity: string
}

interface ActionFormProps {
  action?: ActionWithRelations
  users: User[]
  audits?: Audit[]
  observations?: Observation[]
  onSubmit: (data: CreateActionInput | UpdateActionInput) => Promise<void>
  loading?: boolean
  error?: string | null
  defaultAuditId?: string
  defaultObservationId?: string
}

export function ActionForm({ 
  action, 
  users, 
  audits = [],
  observations = [],
  onSubmit, 
  loading = false, 
  error,
  defaultAuditId,
  defaultObservationId
}: ActionFormProps) {
  const isEditing = !!action

  const form = useForm<CreateActionInput | UpdateActionInput>({
    resolver: zodResolver(isEditing ? updateActionSchema : createActionSchema),
    defaultValues: isEditing ? {
      title: action.title,
      description: action.description,
      dueDate: format(action.dueDate, "yyyy-MM-dd'T'HH:mm"),
      priority: action.priority,
      status: action.status,
      assigneeId: action.assigneeId
    } : {
      title: "",
      description: "",
      dueDate: "",
      priority: ActionPriority.MEDIUM,
      auditId: defaultAuditId || "",
      observationId: defaultObservationId || "",
      assigneeId: ""
    }
  })

  const handleSubmit = async (data: CreateActionInput | UpdateActionInput) => {
    await onSubmit(data)
  }

  const availableUsers = users.filter(user => 
    user.role === "AUDITOR" || user.role === "MANAGER" || user.role === "ADMIN"
  )

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Informations générales */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Informations générales
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Titre de l'action *</Label>
            <Input
              id="title"
              {...form.register("title")}
              placeholder="Ex: Corriger la non-conformité documentaire"
              disabled={loading}
            />
            {form.formState.errors.title && (
              <p className="text-sm text-red-600">{form.formState.errors.title.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              {...form.register("description")}
              placeholder="Description détaillée de l'action à réaliser..."
              rows={4}
              disabled={loading}
            />
            {form.formState.errors.description && (
              <p className="text-sm text-red-600">{form.formState.errors.description.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="priority">Priorité *</Label>
              <Select
                value={form.watch("priority")}
                onValueChange={(value) => form.setValue("priority", value as ActionPriority)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner une priorité" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={ActionPriority.LOW}>Faible</SelectItem>
                  <SelectItem value={ActionPriority.MEDIUM}>Moyenne</SelectItem>
                  <SelectItem value={ActionPriority.HIGH}>Élevée</SelectItem>
                  <SelectItem value={ActionPriority.CRITICAL}>Critique</SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.priority && (
                <p className="text-sm text-red-600">{form.formState.errors.priority.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="dueDate">Date d'échéance *</Label>
              <Input
                id="dueDate"
                type="datetime-local"
                {...form.register("dueDate")}
                disabled={loading}
              />
              {form.formState.errors.dueDate && (
                <p className="text-sm text-red-600">{form.formState.errors.dueDate.message}</p>
              )}
            </div>
          </div>

          {isEditing && (
            <div className="space-y-2">
              <Label htmlFor="status">Statut</Label>
              <Select
                value={form.watch("status")}
                onValueChange={(value) => form.setValue("status", value as ActionStatus)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner un statut" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={ActionStatus.PENDING}>En attente</SelectItem>
                  <SelectItem value={ActionStatus.IN_PROGRESS}>En cours</SelectItem>
                  <SelectItem value={ActionStatus.COMPLETED}>Terminée</SelectItem>
                  <SelectItem value={ActionStatus.CANCELLED}>Annulée</SelectItem>
                  <SelectItem value={ActionStatus.OVERDUE}>En retard</SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.status && (
                <p className="text-sm text-red-600">{form.formState.errors.status.message}</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Assignation et contexte */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Assignation et contexte
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="assigneeId">Assigné à *</Label>
            <Select
              value={form.watch("assigneeId")}
              onValueChange={(value) => form.setValue("assigneeId", value)}
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Sélectionner un assigné" />
              </SelectTrigger>
              <SelectContent>
                {availableUsers.map((user) => (
                  <SelectItem key={user.id} value={user.id}>
                    {user.name || user.email} ({user.role})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.assigneeId && (
              <p className="text-sm text-red-600">{form.formState.errors.assigneeId.message}</p>
            )}
          </div>

          {!isEditing && (
            <>
              <div className="space-y-2">
                <Label htmlFor="auditId">Audit *</Label>
                <Select
                  value={form.watch("auditId")}
                  onValueChange={(value) => form.setValue("auditId", value)}
                  disabled={loading || !!defaultAuditId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un audit" />
                  </SelectTrigger>
                  <SelectContent>
                    {audits.map((audit) => (
                      <SelectItem key={audit.id} value={audit.id}>
                        {audit.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.auditId && (
                  <p className="text-sm text-red-600">{form.formState.errors.auditId.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="observationId">Observation (optionnel)</Label>
                <Select
                  value={form.watch("observationId") || "none"}
                  onValueChange={(value) => form.setValue("observationId", value === "none" ? undefined : value)}
                  disabled={loading || !!defaultObservationId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner une observation" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Aucune observation</SelectItem>
                    {observations.map((observation) => (
                      <SelectItem key={observation.id} value={observation.id}>
                        {observation.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.observationId && (
                  <p className="text-sm text-red-600">{form.formState.errors.observationId.message}</p>
                )}
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex items-center justify-end gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => window.history.back()}
          disabled={loading}
        >
          Annuler
        </Button>
        
        <Button
          type="submit"
          disabled={loading}
        >
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isEditing ? "Mettre à jour" : "Créer l'action"}
        </Button>
      </div>
    </form>
  )
}
