"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ActionTable } from "@/components/features/actions"
import { useActions } from "@/hooks/use-actions"
import { useActionActions } from "@/hooks/use-action-actions"
import { ActionWithRelations, getDaysUntilDue, isActionOverdue } from "@/lib/validations/action"
import { AlertTriangle, Clock, Calendar, CheckCircle, RefreshCw } from "lucide-react"
import { useRouter } from "next/navigation"

interface DueActionsDashboardProps {
  userId?: string
  showOnlyAssigned?: boolean
}

export function DueActionsDashboard({ userId, showOnlyAssigned = false }: DueActionsDashboardProps) {
  const router = useRouter()
  
  const {
    actions: allActions,
    loading,
    error,
    refetch
  } = useActions({
    initialFilters: {
      assigneeId: showOnlyAssigned ? userId : undefined,
      status: undefined, // Récupérer toutes les actions actives
      sortBy: "dueDate",
      sortOrder: "asc",
      limit: 50
    }
  })

  const {
    loading: actionLoading,
    completeAction,
    startAction
  } = useActionActions()

  // Filtrer les actions par catégorie d'échéance
  const overdueActions = allActions.filter(action => 
    isActionOverdue(action) && 
    action.status !== "COMPLETED" && 
    action.status !== "CANCELLED"
  )

  const dueTodayActions = allActions.filter(action => {
    const days = getDaysUntilDue(action.dueDate)
    return days === 0 && 
           action.status !== "COMPLETED" && 
           action.status !== "CANCELLED" &&
           !isActionOverdue(action)
  })

  const dueSoonActions = allActions.filter(action => {
    const days = getDaysUntilDue(action.dueDate)
    return days > 0 && days <= 7 && 
           action.status !== "COMPLETED" && 
           action.status !== "CANCELLED"
  })

  const handleCompleteAction = async (action: ActionWithRelations) => {
    if (confirm(`Marquer l'action "${action.title}" comme terminée ?`)) {
      const completed = await completeAction(action.id)
      if (completed) {
        await refetch()
      }
    }
  }

  const handleStartAction = async (action: ActionWithRelations) => {
    if (confirm(`Démarrer l'action "${action.title}" ?`)) {
      const started = await startAction(action.id)
      if (started) {
        await refetch()
      }
    }
  }

  const handleViewAction = (action: ActionWithRelations) => {
    router.push(`/actions/${action.id}`)
  }

  const handleEditAction = (action: ActionWithRelations) => {
    router.push(`/actions/${action.id}/edit`)
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="h-32 bg-gray-200 rounded animate-pulse" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Erreur lors du chargement des échéances: {error}
        </AlertDescription>
      </Alert>
    )
  }

  const totalCriticalActions = overdueActions.length + dueTodayActions.length

  return (
    <div className="space-y-6">
      {/* Résumé des échéances */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-red-200 bg-red-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-red-800 flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              En retard
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{overdueActions.length}</div>
            <p className="text-xs text-red-600">Actions dépassées</p>
          </CardContent>
        </Card>

        <Card className="border-orange-200 bg-orange-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-orange-800 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Aujourd'hui
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{dueTodayActions.length}</div>
            <p className="text-xs text-orange-600">À échéance aujourd'hui</p>
          </CardContent>
        </Card>

        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-yellow-800 flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Cette semaine
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{dueSoonActions.length}</div>
            <p className="text-xs text-yellow-600">Dans les 7 prochains jours</p>
          </CardContent>
        </Card>

        <Card className="border-blue-200 bg-blue-50">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-blue-800 flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Total actif
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{allActions.length}</div>
            <p className="text-xs text-blue-600">Actions en cours</p>
          </CardContent>
        </Card>
      </div>

      {/* Actions critiques (en retard + aujourd'hui) */}
      {totalCriticalActions > 0 && (
        <Card className="border-red-200">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-red-800 flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Actions critiques ({totalCriticalActions})
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                disabled={loading || actionLoading}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Actualiser
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <ActionTable
              actions={[...overdueActions, ...dueTodayActions]}
              onView={handleViewAction}
              onEdit={handleEditAction}
              onComplete={handleCompleteAction}
              onStart={handleStartAction}
              loading={actionLoading}
              showAuditInfo={!showOnlyAssigned}
              showObservationInfo={true}
            />
          </CardContent>
        </Card>
      )}

      {/* Actions à échéance cette semaine */}
      {dueSoonActions.length > 0 && (
        <Card className="border-yellow-200">
          <CardHeader>
            <CardTitle className="text-yellow-800 flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              À échéance cette semaine ({dueSoonActions.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ActionTable
              actions={dueSoonActions}
              onView={handleViewAction}
              onEdit={handleEditAction}
              onComplete={handleCompleteAction}
              onStart={handleStartAction}
              loading={actionLoading}
              showAuditInfo={!showOnlyAssigned}
              showObservationInfo={true}
            />
          </CardContent>
        </Card>
      )}

      {/* Message si aucune action critique */}
      {totalCriticalActions === 0 && dueSoonActions.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <CheckCircle className="mx-auto h-12 w-12 text-green-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucune action urgente
            </h3>
            <p className="text-gray-500">
              {showOnlyAssigned 
                ? "Vous n'avez aucune action en retard ou à échéance proche."
                : "Aucune action n'est en retard ou à échéance proche."
              }
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
