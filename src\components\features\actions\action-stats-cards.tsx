import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { ActionStats, ActionStatus, ActionPriority } from "@/lib/validations/action"
import { CheckCircle, Clock, AlertTriangle, TrendingUp, Calendar, Target } from "lucide-react"

interface ActionStatsCardsProps {
  stats: ActionStats
  loading?: boolean
}

export function ActionStatsCards({ stats, loading }: ActionStatsCardsProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="h-16 bg-gray-200 rounded animate-pulse" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const completionRateColor = stats.completionRate >= 80 ? "text-green-600" : 
                             stats.completionRate >= 60 ? "text-yellow-600" : "text-red-600"

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Total des actions */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total des actions</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total}</div>
          <p className="text-xs text-muted-foreground">
            {stats.byStatus[ActionStatus.COMPLETED]} terminées
          </p>
        </CardContent>
      </Card>

      {/* Actions en retard */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">En retard</CardTitle>
          <AlertTriangle className="h-4 w-4 text-red-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">{stats.overdue}</div>
          <p className="text-xs text-muted-foreground">
            Actions dépassées
          </p>
        </CardContent>
      </Card>

      {/* Échéances cette semaine */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Cette semaine</CardTitle>
          <Calendar className="h-4 w-4 text-orange-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-600">{stats.dueThisWeek}</div>
          <p className="text-xs text-muted-foreground">
            À échéance dans 7 jours
          </p>
        </CardContent>
      </Card>

      {/* Taux de completion */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Taux de completion</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${completionRateColor}`}>
            {stats.completionRate.toFixed(1)}%
          </div>
          <p className="text-xs text-muted-foreground">
            Actions terminées
          </p>
        </CardContent>
      </Card>

      {/* Répartition par statut */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium">Répartition par statut</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-yellow-500" />
                <span className="text-sm">En attente</span>
              </div>
              <span className="text-sm font-medium">{stats.byStatus[ActionStatus.PENDING]}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-500" />
                <span className="text-sm">En cours</span>
              </div>
              <span className="text-sm font-medium">{stats.byStatus[ActionStatus.IN_PROGRESS]}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Terminées</span>
              </div>
              <span className="text-sm font-medium">{stats.byStatus[ActionStatus.COMPLETED]}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <span className="text-sm">En retard</span>
              </div>
              <span className="text-sm font-medium">{stats.byStatus[ActionStatus.OVERDUE]}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Répartition par priorité */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium">Répartition par priorité</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <span className="text-sm">Critique</span>
              </div>
              <span className="text-sm font-medium">{stats.byPriority[ActionPriority.CRITICAL]}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-orange-500" />
                <span className="text-sm">Élevée</span>
              </div>
              <span className="text-sm font-medium">{stats.byPriority[ActionPriority.HIGH]}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-500" />
                <span className="text-sm">Moyenne</span>
              </div>
              <span className="text-sm font-medium">{stats.byPriority[ActionPriority.MEDIUM]}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <span className="text-sm">Faible</span>
              </div>
              <span className="text-sm font-medium">{stats.byPriority[ActionPriority.LOW]}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
