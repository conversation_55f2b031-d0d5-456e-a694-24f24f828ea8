import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ReportService } from "@/lib/services/report-service"
import { exportReportSchema } from "@/lib/validations/report"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"
import { z } from "zod"

/**
 * POST /api/reports/[id]/export - Exporter un rapport
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'reports', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que le rapport existe et que l'utilisateur y a accès
      const report = await ReportService.getReportById(params.id)
      
      if (!report) {
        return NextResponse.json(
          { success: false, error: "Rapport non trouvé" },
          { status: 404 }
        )
      }

      // Vérifier l'accès organisationnel
      if (user.role !== "SUPER_ADMIN" && user.organizationId !== report.organizationId) {
        return NextResponse.json(
          { success: false, error: "Accès non autorisé" },
          { status: 403 }
        )
      }

      const body = await req.json()
      const validatedData = exportReportSchema.parse(body)

      const exportedData = await ReportService.exportReport(params.id, validatedData)

      // Définir le type de contenu selon le format
      const contentTypes = {
        PDF: 'application/pdf',
        WORD: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        EXCEL: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        HTML: 'text/html'
      }

      const extensions = {
        PDF: 'pdf',
        WORD: 'docx',
        EXCEL: 'xlsx',
        HTML: 'html'
      }

      const contentType = contentTypes[validatedData.format]
      const extension = extensions[validatedData.format]
      const filename = `${report.title.replace(/[^a-zA-Z0-9]/g, '_')}.${extension}`

      return new NextResponse(exportedData, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': exportedData.length.toString()
        }
      })

    } catch (error) {
      console.error("Erreur lors de l'export du rapport:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Données invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
