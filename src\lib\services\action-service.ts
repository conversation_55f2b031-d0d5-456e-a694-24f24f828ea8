import { prisma } from "@/lib/prisma"
import { 
  CreateActionInput, 
  UpdateActionInput, 
  ActionFilters, 
  ActionWithRelations,
  ActionStats,
  ActionStatus,
  ActionPriority,
  AssignActionInput,
  CompleteActionInput,
  isActionOverdue
} from "@/lib/validations/action"
import { Prisma } from "@prisma/client"

export class ActionService {
  /**
   * Créer une nouvelle action
   */
  static async createAction(data: CreateActionInput, creatorId: string): Promise<ActionWithRelations> {
    // Vérifier que l'audit existe
    const audit = await prisma.audit.findUnique({
      where: { id: data.auditId },
      include: {
        organization: {
          select: { id: true, name: true }
        }
      }
    })

    if (!audit) {
      throw new Error("Audit non trouvé")
    }

    // Vérifier que l'assigné existe et est actif
    const assignee = await prisma.user.findUnique({
      where: { id: data.assigneeId, isActive: true }
    })

    if (!assignee) {
      throw new Error("Assigné non trouvé ou inactif")
    }

    // Si une observation est spécifiée, vérifier qu'elle existe
    if (data.observationId) {
      const observation = await prisma.observation.findUnique({
        where: { id: data.observationId }
      })

      if (!observation) {
        throw new Error("Observation non trouvée")
      }
    }

    const action = await prisma.action.create({
      data: {
        title: data.title,
        description: data.description,
        dueDate: new Date(data.dueDate),
        priority: data.priority,
        auditId: data.auditId,
        observationId: data.observationId,
        assigneeId: data.assigneeId,
        creatorId
      },
      include: {
        assignee: {
          select: { id: true, name: true, email: true }
        },
        creator: {
          select: { id: true, name: true, email: true }
        },
        audit: {
          select: { id: true, title: true, status: true }
        },
        observation: {
          select: { id: true, title: true, severity: true }
        }
      }
    })

    return action as ActionWithRelations
  }

  /**
   * Obtenir une action par ID
   */
  static async getActionById(id: string): Promise<ActionWithRelations | null> {
    const action = await prisma.action.findUnique({
      where: { id },
      include: {
        assignee: {
          select: { id: true, name: true, email: true }
        },
        creator: {
          select: { id: true, name: true, email: true }
        },
        audit: {
          select: { id: true, title: true, status: true }
        },
        observation: {
          select: { id: true, title: true, severity: true }
        }
      }
    })

    return action as ActionWithRelations | null
  }

  /**
   * Obtenir la liste des actions avec filtres et pagination
   */
  static async getActions(filters: ActionFilters): Promise<{
    actions: ActionWithRelations[]
    total: number
    page: number
    limit: number
    totalPages: number
  }> {
    const { page, limit, sortBy, sortOrder, search, includeOverdue, ...filterParams } = filters

    // Construire les conditions de filtrage
    const where: Prisma.ActionWhereInput = {}

    if (filterParams.auditId) {
      where.auditId = filterParams.auditId
    }

    if (filterParams.observationId) {
      where.observationId = filterParams.observationId
    }

    if (filterParams.assigneeId) {
      where.assigneeId = filterParams.assigneeId
    }

    if (filterParams.creatorId) {
      where.creatorId = filterParams.creatorId
    }

    if (filterParams.status) {
      where.status = filterParams.status
    }

    if (filterParams.priority) {
      where.priority = filterParams.priority
    }

    if (filterParams.dueDateFrom || filterParams.dueDateTo) {
      where.dueDate = {}
      if (filterParams.dueDateFrom) {
        where.dueDate.gte = new Date(filterParams.dueDateFrom)
      }
      if (filterParams.dueDateTo) {
        where.dueDate.lte = new Date(filterParams.dueDateTo)
      }
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Calculer le total
    const total = await prisma.action.count({ where })

    // Obtenir les actions avec pagination
    const actions = await prisma.action.findMany({
      where,
      include: {
        assignee: {
          select: { id: true, name: true, email: true }
        },
        creator: {
          select: { id: true, name: true, email: true }
        },
        audit: {
          select: { id: true, title: true, status: true }
        },
        observation: {
          select: { id: true, title: true, severity: true }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip: (page - 1) * limit,
      take: limit
    })

    // Mettre à jour le statut des actions en retard si nécessaire
    const actionsWithStatus = await this.updateOverdueActions(actions as ActionWithRelations[])

    return {
      actions: actionsWithStatus,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  }

  /**
   * Obtenir les actions d'un audit spécifique
   */
  static async getActionsByAudit(auditId: string): Promise<ActionWithRelations[]> {
    const actions = await prisma.action.findMany({
      where: { auditId },
      include: {
        assignee: {
          select: { id: true, name: true, email: true }
        },
        creator: {
          select: { id: true, name: true, email: true }
        },
        audit: {
          select: { id: true, title: true, status: true }
        },
        observation: {
          select: { id: true, title: true, severity: true }
        }
      },
      orderBy: [
        { priority: 'desc' }, // Critiques en premier
        { dueDate: 'asc' }    // Plus urgentes en premier
      ]
    })

    return this.updateOverdueActions(actions as ActionWithRelations[])
  }

  /**
   * Obtenir les actions d'une observation spécifique
   */
  static async getActionsByObservation(observationId: string): Promise<ActionWithRelations[]> {
    const actions = await prisma.action.findMany({
      where: { observationId },
      include: {
        assignee: {
          select: { id: true, name: true, email: true }
        },
        creator: {
          select: { id: true, name: true, email: true }
        },
        audit: {
          select: { id: true, title: true, status: true }
        },
        observation: {
          select: { id: true, title: true, severity: true }
        }
      },
      orderBy: [
        { priority: 'desc' },
        { dueDate: 'asc' }
      ]
    })

    return this.updateOverdueActions(actions as ActionWithRelations[])
  }

  /**
   * Mettre à jour une action
   */
  static async updateAction(id: string, data: UpdateActionInput): Promise<ActionWithRelations> {
    const updateData: Prisma.ActionUpdateInput = {}

    if (data.title !== undefined) updateData.title = data.title
    if (data.description !== undefined) updateData.description = data.description
    if (data.dueDate !== undefined) updateData.dueDate = new Date(data.dueDate)
    if (data.status !== undefined) updateData.status = data.status
    if (data.priority !== undefined) updateData.priority = data.priority
    if (data.assigneeId !== undefined) updateData.assigneeId = data.assigneeId
    if (data.completedAt !== undefined) {
      updateData.completedAt = data.completedAt ? new Date(data.completedAt) : null
    }

    const action = await prisma.action.update({
      where: { id },
      data: updateData,
      include: {
        assignee: {
          select: { id: true, name: true, email: true }
        },
        creator: {
          select: { id: true, name: true, email: true }
        },
        audit: {
          select: { id: true, title: true, status: true }
        },
        observation: {
          select: { id: true, title: true, severity: true }
        }
      }
    })

    return action as ActionWithRelations
  }

  /**
   * Supprimer une action
   */
  static async deleteAction(id: string): Promise<void> {
    await prisma.action.delete({
      where: { id }
    })
  }

  /**
   * Assigner une action à un utilisateur
   */
  static async assignAction(id: string, data: AssignActionInput): Promise<ActionWithRelations> {
    // Vérifier que l'assigné existe et est actif
    const assignee = await prisma.user.findUnique({
      where: { id: data.assigneeId, isActive: true }
    })

    if (!assignee) {
      throw new Error("Assigné non trouvé ou inactif")
    }

    return this.updateAction(id, { assigneeId: data.assigneeId })
  }

  /**
   * Marquer une action comme terminée
   */
  static async completeAction(id: string, data?: CompleteActionInput): Promise<ActionWithRelations> {
    const completedAt = data?.completedAt || new Date().toISOString()
    
    return this.updateAction(id, { 
      status: ActionStatus.COMPLETED,
      completedAt
    })
  }

  /**
   * Marquer une action comme en cours
   */
  static async startAction(id: string): Promise<ActionWithRelations> {
    return this.updateAction(id, { status: ActionStatus.IN_PROGRESS })
  }

  /**
   * Annuler une action
   */
  static async cancelAction(id: string): Promise<ActionWithRelations> {
    return this.updateAction(id, { status: ActionStatus.CANCELLED })
  }

  /**
   * Rouvrir une action
   */
  static async reopenAction(id: string): Promise<ActionWithRelations> {
    return this.updateAction(id, { 
      status: ActionStatus.PENDING,
      completedAt: null
    })
  }

  /**
   * Obtenir les statistiques des actions
   */
  static async getActionStats(auditId?: string): Promise<ActionStats> {
    const where: Prisma.ActionWhereInput = auditId ? { auditId } : {}

    const actions = await prisma.action.findMany({
      where,
      select: {
        status: true,
        priority: true,
        dueDate: true,
        completedAt: true
      }
    })

    const total = actions.length
    const now = new Date()
    const oneWeekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
    const oneMonthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)

    // Statistiques par statut
    const byStatus = {
      [ActionStatus.PENDING]: 0,
      [ActionStatus.IN_PROGRESS]: 0,
      [ActionStatus.COMPLETED]: 0,
      [ActionStatus.CANCELLED]: 0,
      [ActionStatus.OVERDUE]: 0
    }

    // Statistiques par priorité
    const byPriority = {
      [ActionPriority.LOW]: 0,
      [ActionPriority.MEDIUM]: 0,
      [ActionPriority.HIGH]: 0,
      [ActionPriority.CRITICAL]: 0
    }

    let overdue = 0
    let dueThisWeek = 0
    let dueThisMonth = 0
    let completed = 0

    actions.forEach(action => {
      // Compter par statut
      byStatus[action.status as ActionStatus]++

      // Compter par priorité
      byPriority[action.priority as ActionPriority]++

      // Vérifier si en retard
      if (action.status !== ActionStatus.COMPLETED &&
          action.status !== ActionStatus.CANCELLED &&
          new Date(action.dueDate) < now) {
        overdue++
      }

      // Vérifier échéances
      const dueDate = new Date(action.dueDate)
      if (action.status !== ActionStatus.COMPLETED &&
          action.status !== ActionStatus.CANCELLED) {
        if (dueDate <= oneWeekFromNow) {
          dueThisWeek++
        }
        if (dueDate <= oneMonthFromNow) {
          dueThisMonth++
        }
      }

      // Compter les terminées
      if (action.status === ActionStatus.COMPLETED) {
        completed++
      }
    })

    const completionRate = total > 0 ? (completed / total) * 100 : 0

    return {
      total,
      byStatus,
      byPriority,
      overdue,
      dueThisWeek,
      dueThisMonth,
      completionRate
    }
  }

  /**
   * Obtenir les actions en retard
   */
  static async getOverdueActions(auditId?: string): Promise<ActionWithRelations[]> {
    const where: Prisma.ActionWhereInput = {
      dueDate: { lt: new Date() },
      status: {
        notIn: [ActionStatus.COMPLETED, ActionStatus.CANCELLED]
      }
    }

    if (auditId) {
      where.auditId = auditId
    }

    const actions = await prisma.action.findMany({
      where,
      include: {
        assignee: {
          select: { id: true, name: true, email: true }
        },
        creator: {
          select: { id: true, name: true, email: true }
        },
        audit: {
          select: { id: true, title: true, status: true }
        },
        observation: {
          select: { id: true, title: true, severity: true }
        }
      },
      orderBy: { dueDate: 'asc' }
    })

    return this.updateOverdueActions(actions as ActionWithRelations[])
  }

  /**
   * Obtenir les actions assignées à un utilisateur
   */
  static async getActionsByAssignee(assigneeId: string): Promise<ActionWithRelations[]> {
    const actions = await prisma.action.findMany({
      where: {
        assigneeId,
        status: {
          notIn: [ActionStatus.COMPLETED, ActionStatus.CANCELLED]
        }
      },
      include: {
        assignee: {
          select: { id: true, name: true, email: true }
        },
        creator: {
          select: { id: true, name: true, email: true }
        },
        audit: {
          select: { id: true, title: true, status: true }
        },
        observation: {
          select: { id: true, title: true, severity: true }
        }
      },
      orderBy: [
        { priority: 'desc' },
        { dueDate: 'asc' }
      ]
    })

    return this.updateOverdueActions(actions as ActionWithRelations[])
  }

  /**
   * Mettre à jour le statut des actions en retard
   */
  private static async updateOverdueActions(actions: ActionWithRelations[]): Promise<ActionWithRelations[]> {
    const overdueActions = actions.filter(action =>
      isActionOverdue(action) && action.status !== ActionStatus.OVERDUE
    )

    if (overdueActions.length > 0) {
      // Mettre à jour en base les actions en retard
      await prisma.action.updateMany({
        where: {
          id: { in: overdueActions.map(a => a.id) }
        },
        data: {
          status: ActionStatus.OVERDUE
        }
      })

      // Mettre à jour le statut dans les objets retournés
      overdueActions.forEach(action => {
        action.status = ActionStatus.OVERDUE
      })
    }

    return actions
  }
}
