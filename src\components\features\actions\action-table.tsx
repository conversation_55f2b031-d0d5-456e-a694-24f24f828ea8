"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ActionStatusBadge } from "./action-status-badge"
import { ActionPriorityBadge } from "./action-priority-badge"
import { ActionWithRelations, getDaysUntilDue, isActionOverdue } from "@/lib/validations/action"
import { Eye, Edit, Trash2, CheckCircle, Play, RotateCcw, User, Calendar } from "lucide-react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

interface ActionTableProps {
  actions: ActionWithRelations[]
  onView?: (action: ActionWithRelations) => void
  onEdit?: (action: ActionWithRelations) => void
  onDelete?: (action: ActionWithRelations) => void
  onComplete?: (action: ActionWithRelations) => void
  onStart?: (action: ActionWithRelations) => void
  onReopen?: (action: ActionWithRelations) => void
  loading?: boolean
  showAuditInfo?: boolean
  showObservationInfo?: boolean
}

export function ActionTable({
  actions,
  onView,
  onEdit,
  onDelete,
  onComplete,
  onStart,
  onReopen,
  loading = false,
  showAuditInfo = true,
  showObservationInfo = true
}: ActionTableProps) {
  const formatDate = (date: Date) => {
    return format(date, "dd/MM/yyyy", { locale: fr })
  }

  const getDueDateColor = (action: ActionWithRelations) => {
    const daysUntilDue = getDaysUntilDue(action.dueDate)
    const overdue = isActionOverdue(action)
    
    if (overdue) return "text-red-600"
    if (daysUntilDue <= 3) return "text-orange-600"
    if (daysUntilDue <= 7) return "text-yellow-600"
    return "text-gray-900"
  }

  const getDueDateText = (action: ActionWithRelations) => {
    const daysUntilDue = getDaysUntilDue(action.dueDate)
    const overdue = isActionOverdue(action)
    
    if (overdue) {
      const daysPast = Math.abs(daysUntilDue)
      return `En retard de ${daysPast}j`
    }
    if (daysUntilDue === 0) return "Aujourd'hui"
    if (daysUntilDue === 1) return "Demain"
    return `Dans ${daysUntilDue}j`
  }

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-100 rounded animate-pulse" />
        ))}
      </div>
    )
  }

  if (actions.length === 0) {
    return (
      <div className="text-center py-12">
        <CheckCircle className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune action</h3>
        <p className="mt-1 text-sm text-gray-500">
          Aucune action trouvée avec les critères actuels.
        </p>
      </div>
    )
  }

  return (
    <div className="border rounded-lg overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Action</TableHead>
            <TableHead>Statut</TableHead>
            <TableHead>Priorité</TableHead>
            <TableHead>Assigné à</TableHead>
            <TableHead>Échéance</TableHead>
            {showAuditInfo && <TableHead>Audit</TableHead>}
            {showObservationInfo && <TableHead>Observation</TableHead>}
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {actions.map((action) => (
            <TableRow key={action.id} className="hover:bg-gray-50">
              <TableCell>
                <div>
                  <p className="font-medium line-clamp-1">{action.title}</p>
                  <p className="text-sm text-gray-500 line-clamp-1">{action.description}</p>
                </div>
              </TableCell>
              
              <TableCell>
                <ActionStatusBadge status={action.status} />
              </TableCell>
              
              <TableCell>
                <ActionPriorityBadge priority={action.priority} />
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{action.assignee.name || action.assignee.email}</span>
                </div>
              </TableCell>
              
              <TableCell>
                <div>
                  <p className={`text-sm font-medium ${getDueDateColor(action)}`}>
                    {formatDate(action.dueDate)}
                  </p>
                  <p className={`text-xs ${getDueDateColor(action)}`}>
                    {getDueDateText(action)}
                  </p>
                </div>
              </TableCell>
              
              {showAuditInfo && (
                <TableCell>
                  <span className="text-sm">{action.audit.title}</span>
                </TableCell>
              )}
              
              {showObservationInfo && (
                <TableCell>
                  {action.observation ? (
                    <span className="text-sm">{action.observation.title}</span>
                  ) : (
                    <span className="text-sm text-gray-400">-</span>
                  )}
                </TableCell>
              )}
              
              <TableCell className="text-right">
                <div className="flex items-center justify-end gap-1">
                  {onView && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onView(action)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  )}
                  
                  {onEdit && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEdit(action)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  )}
                  
                  {onStart && action.status === "PENDING" && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onStart(action)}
                      className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                    >
                      <Play className="h-4 w-4" />
                    </Button>
                  )}
                  
                  {onComplete && (action.status === "PENDING" || action.status === "IN_PROGRESS" || action.status === "OVERDUE") && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onComplete(action)}
                      className="text-green-600 hover:text-green-700 hover:bg-green-50"
                    >
                      <CheckCircle className="h-4 w-4" />
                    </Button>
                  )}
                  
                  {onReopen && (action.status === "COMPLETED" || action.status === "CANCELLED") && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onReopen(action)}
                      className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                  )}
                  
                  {onDelete && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDelete(action)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
