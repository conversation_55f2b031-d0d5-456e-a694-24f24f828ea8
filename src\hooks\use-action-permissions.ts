"use client"

import { useSession } from "@/lib/auth/client"
import { hasPermission } from "@/lib/validations/user"
import { ActionWithRelations } from "@/lib/validations/action"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"

interface UseActionPermissionsResult {
  canRead: () => boolean
  canCreate: () => boolean
  canUpdate: (action?: ActionWithRelations) => boolean
  canDelete: (action?: ActionWithRelations) => boolean
  canAssign: (action?: ActionWithRelations) => boolean
  canComplete: (action?: ActionWithRelations) => boolean
  canReopen: (action?: ActionWithRelations) => boolean
  user: any
}

export function useActionPermissions(action?: ActionWithRelations): UseActionPermissionsResult {
  const { data: session } = useSession()
  const user = session?.user

  const canRead = (): boolean => {
    if (!user) return false
    return hasPermission(user.role as UserRole, 'actions', 'read')
  }

  const canCreate = (): boolean => {
    if (!user) return false
    return hasPermission(user.role as UserRole, 'actions', 'create')
  }

  const canUpdate = (targetAction?: ActionWithRelations): boolean => {
    if (!user) return false
    
    // Vérifier les permissions générales
    const hasGeneralPermission = hasPermission(user.role as UserRole, 'actions', 'update')
    
    // Si pas d'action spécifique, retourner la permission générale
    if (!targetAction) return hasGeneralPermission
    
    // L'assigné peut toujours modifier sa propre action
    const isAssignee = targetAction.assigneeId === user.id
    
    return hasGeneralPermission || isAssignee
  }

  const canDelete = (targetAction?: ActionWithRelations): boolean => {
    if (!user) return false
    
    // Seuls les admins et managers peuvent supprimer des actions
    const hasGeneralPermission = hasPermission(user.role as UserRole, 'actions', 'delete')
    
    // Si pas d'action spécifique, retourner la permission générale
    if (!targetAction) return hasGeneralPermission
    
    // Le créateur peut supprimer sa propre action si elle n'est pas encore en cours
    const isCreator = targetAction.creatorId === user.id
    const isNotStarted = targetAction.status === "PENDING"
    
    return hasGeneralPermission || (isCreator && isNotStarted)
  }

  const canAssign = (targetAction?: ActionWithRelations): boolean => {
    if (!user) return false
    
    // Vérifier les permissions générales
    const hasGeneralPermission = hasPermission(user.role as UserRole, 'actions', 'update')
    
    // Si pas d'action spécifique, retourner la permission générale
    if (!targetAction) return hasGeneralPermission
    
    // Seuls les managers et admins peuvent réassigner des actions
    return user.role === "ADMIN" || user.role === "MANAGER" || user.role === "SUPER_ADMIN"
  }

  const canComplete = (targetAction?: ActionWithRelations): boolean => {
    if (!user) return false
    
    // Si pas d'action spécifique, vérifier les permissions générales
    if (!targetAction) {
      return hasPermission(user.role as UserRole, 'actions', 'update')
    }
    
    // L'assigné peut toujours marquer sa propre action comme terminée
    const isAssignee = targetAction.assigneeId === user.id
    
    // Les managers et admins peuvent marquer n'importe quelle action comme terminée
    const hasGeneralPermission = hasPermission(user.role as UserRole, 'actions', 'update')
    
    // L'action doit être en attente ou en cours pour être terminée
    const canBeCompleted = targetAction.status === "PENDING" || 
                           targetAction.status === "IN_PROGRESS" || 
                           targetAction.status === "OVERDUE"
    
    return (isAssignee || hasGeneralPermission) && canBeCompleted
  }

  const canReopen = (targetAction?: ActionWithRelations): boolean => {
    if (!user) return false
    
    // Si pas d'action spécifique, vérifier les permissions générales
    if (!targetAction) {
      return hasPermission(user.role as UserRole, 'actions', 'update')
    }
    
    // Seuls les managers et admins peuvent rouvrir des actions
    const hasGeneralPermission = user.role === "ADMIN" || 
                                user.role === "MANAGER" || 
                                user.role === "SUPER_ADMIN"
    
    // L'action doit être terminée ou annulée pour être rouverte
    const canBeReopened = targetAction.status === "COMPLETED" || 
                         targetAction.status === "CANCELLED"
    
    return hasGeneralPermission && canBeReopened
  }

  return {
    canRead,
    canCreate,
    canUpdate,
    canDelete,
    canAssign,
    canComplete,
    canReopen,
    user
  }
}
