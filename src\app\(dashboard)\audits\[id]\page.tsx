"use client"

import { use<PERSON>ffe<PERSON>, useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AuditStatusBadge } from "@/components/features/audits"
import { AuditAuditorsManager } from "@/components/features/audits/audit-auditors-manager"
import { AuditStatsCards } from "@/components/features/audits/audit-stats-cards"
import { AuditTimeline } from "@/components/features/audits/audit-timeline"
import { useAuditActions } from "@/hooks/use-audit-actions"
import { useFormData } from "@/hooks/use-form-data"
import { useAuditPermissions } from "@/hooks/use-audit-permissions"
import { useObservations } from "@/hooks/use-observations"
import { useO<PERSON>ervationActions } from "@/hooks/use-observation-actions"
import { ObservationTable } from "@/components/features/observations"
import { useAuditActions as useAuditActionsHook } from "@/hooks/use-actions"
import { useActionActions } from "@/hooks/use-action-actions"
import { useActionPermissions } from "@/hooks/use-action-permissions"
import { ActionTable } from "@/components/features/actions"
import { AssignAuditorsInput } from "@/lib/validations/audit"
import { AuditWithRelations } from "@/lib/validations/audit"
import { ArrowLeft, Edit, Users, Calendar, FileText, AlertTriangle, Plus, CheckCircle, Play, RotateCcw, Trash2 } from "lucide-react"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

interface AuditDetailPageProps {
  params: {
    id: string
  }
}

export default function AuditDetailPage({ params }: AuditDetailPageProps) {
  const router = useRouter()
  const [audit, setAudit] = useState<AuditWithRelations | null>(null)
  
  const {
    loading,
    error,
    getAudit,
    clearError
  } = useAuditActions()

  const {
    users,
    loading: usersLoading,
    error: usersError
  } = useFormData()

  const {
    canUpdate,
    canAssignAuditors
  } = useAuditPermissions(audit || undefined)

  // Hook pour les observations de cet audit
  const {
    observations,
    loading: observationsLoading,
    error: observationsError,
    refetch: refetchObservations
  } = useObservations({ auditId: params.id })

  const {
    loading: observationActionLoading,
    error: observationActionError,
    resolveObservation,
    closeObservation,
    reopenObservation,
    deleteObservation
  } = useObservationActions()

  // Hook pour les actions de cet audit
  const {
    actions,
    loading: actionsLoading,
    error: actionsError,
    refetch: refetchActions
  } = useAuditActionsHook(params.id)

  const {
    loading: actionActionLoading,
    error: actionActionError,
    completeAction,
    startAction,
    reopenAction,
    deleteAction: deleteActionAction
  } = useActionActions()

  const {
    canCreate: canCreateAction,
    canUpdate: canUpdateAction,
    canDelete: canDeleteAction,
    canComplete: canCompleteAction
  } = useActionPermissions()

  useEffect(() => {
    const fetchAudit = async () => {
      const auditData = await getAudit(params.id)
      if (auditData) {
        setAudit(auditData)
      }
    }

    fetchAudit()
  }, [params.id, getAudit])

  const handleAssignAuditors = async (data: AssignAuditorsInput) => {
    try {
      const response = await fetch(`/api/audits/${params.id}/auditors`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        // Recharger l'audit pour voir les changements
        const updatedAudit = await getAudit(params.id)
        if (updatedAudit) {
          setAudit(updatedAudit)
        }
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erreur lors de l'assignation des auditeurs")
      }
    } catch (error) {
      console.error("Erreur lors de l'assignation des auditeurs:", error)
      throw error
    }
  }

  // Handlers pour les observations
  const handleViewObservation = (observation: any) => {
    router.push(`/observations/${observation.id}`)
  }

  const handleEditObservation = (observation: any) => {
    router.push(`/observations/${observation.id}/edit`)
  }

  const handleDeleteObservation = async (observation: any) => {
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'observation "${observation.title}" ?`)) {
      const success = await deleteObservation(observation.id)
      if (success) {
        await refetchObservations()
      }
    }
  }

  const handleResolveObservation = async (observation: any) => {
    if (confirm(`Marquer l'observation "${observation.title}" comme résolue ?`)) {
      const resolved = await resolveObservation(observation.id)
      if (resolved) {
        await refetchObservations()
      }
    }
  }

  const handleCloseObservation = async (observation: any) => {
    if (confirm(`Fermer définitivement l'observation "${observation.title}" ?`)) {
      const closed = await closeObservation(observation.id)
      if (closed) {
        await refetchObservations()
      }
    }
  }

  const handleReopenObservation = async (observation: any) => {
    if (confirm(`Rouvrir l'observation "${observation.title}" ?`)) {
      const reopened = await reopenObservation(observation.id)
      if (reopened) {
        await refetchObservations()
      }
    }
  }

  // Handlers pour les actions
  const handleViewAction = (action: any) => {
    router.push(`/actions/${action.id}`)
  }

  const handleEditAction = (action: any) => {
    router.push(`/actions/${action.id}/edit`)
  }

  const handleDeleteAction = async (action: any) => {
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'action "${action.title}" ?`)) {
      const success = await deleteActionAction(action.id)
      if (success) {
        await refetchActions()
      }
    }
  }

  const handleCompleteAction = async (action: any) => {
    if (confirm(`Marquer l'action "${action.title}" comme terminée ?`)) {
      const completed = await completeAction(action.id)
      if (completed) {
        await refetchActions()
      }
    }
  }

  const handleStartAction = async (action: any) => {
    if (confirm(`Démarrer l'action "${action.title}" ?`)) {
      const started = await startAction(action.id)
      if (started) {
        await refetchActions()
      }
    }
  }

  const handleReopenAction = async (action: any) => {
    if (confirm(`Rouvrir l'action "${action.title}" ?`)) {
      const reopened = await reopenAction(action.id)
      if (reopened) {
        await refetchActions()
      }
    }
  }

  const formatDate = (date: Date) => {
    return format(date, "dd MMMM yyyy", { locale: fr })
  }

  const formatDateTime = (date: Date) => {
    return format(date, "dd/MM/yyyy à HH:mm", { locale: fr })
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="h-64 bg-gray-200 rounded animate-pulse" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-2"
            >
              Fermer
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!audit) {
    return (
      <div className="space-y-6">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Audit non trouvé
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  const leadAuditor = audit.auditors.find(a => a.role === "LEAD_AUDITOR")

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          
          <div>
            <h1 className="text-3xl font-bold magneto-title">{audit.title}</h1>
            <p className="text-gray-600">
              {audit.organization.name}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <AuditStatusBadge status={audit.status} />
          {canUpdate && (
            <Button
              variant="outline"
              onClick={() => router.push(`/audits/${audit.id}/edit`)}
            >
              <Edit className="h-4 w-4 mr-2" />
              Modifier
            </Button>
          )}
        </div>
      </div>

      {/* Informations principales */}
      <AuditStatsCards audit={audit} />

      {/* Onglets de détail */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="auditors">Auditeurs</TabsTrigger>
          <TabsTrigger value="observations">Observations</TabsTrigger>
          <TabsTrigger value="actions">Actions</TabsTrigger>
          <TabsTrigger value="reports">Rapports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Description</CardTitle>
                </CardHeader>
                <CardContent>
                  {audit.description ? (
                    <p className="text-gray-700 whitespace-pre-wrap">{audit.description}</p>
                  ) : (
                    <p className="text-gray-500 italic">Aucune description fournie</p>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Informations système</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Créé le</p>
                      <p className="font-medium">{formatDateTime(audit.createdAt)}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Modifié le</p>
                      <p className="font-medium">{formatDateTime(audit.updatedAt)}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Créé par</p>
                      <p className="font-medium">{audit.creator.name || audit.creator.email}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div>
              <AuditTimeline audit={audit} />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="auditors" className="space-y-4">
          {canAssignAuditors ? (
            <AuditAuditorsManager
              audit={audit}
              users={users}
              onAssignAuditors={handleAssignAuditors}
              loading={usersLoading}
              error={usersError}
            />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Équipe d'audit ({audit.auditors.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {audit.auditors.map((auditor) => (
                    <div key={auditor.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{auditor.user.name || auditor.user.email}</p>
                        <p className="text-sm text-gray-500">{auditor.user.email}</p>
                      </div>
                      <Badge variant={auditor.role === "LEAD_AUDITOR" ? "default" : "secondary"}>
                        {auditor.role === "LEAD_AUDITOR" ? "Auditeur principal" :
                         auditor.role === "AUDITOR" ? "Auditeur" : "Observateur"}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="observations" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Observations ({observations.length})
                </CardTitle>
                <Button
                  onClick={() => router.push(`/observations/new?auditId=${audit.id}`)}
                  size="sm"
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Nouvelle observation
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {observationsError && (
                <Alert variant="destructive" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{observationsError}</AlertDescription>
                </Alert>
              )}

              {observationActionError && (
                <Alert variant="destructive" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{observationActionError}</AlertDescription>
                </Alert>
              )}

              <ObservationTable
                observations={observations}
                onView={handleViewObservation}
                onEdit={handleEditObservation}
                onDelete={handleDeleteObservation}
                onResolve={handleResolveObservation}
                onClose={handleCloseObservation}
                onReopen={handleReopenObservation}
                loading={observationsLoading || observationActionLoading}
                showAuditInfo={false}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="actions" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Actions correctives ({actions.length})
                </CardTitle>
                {canCreateAction() && (
                  <Button
                    onClick={() => router.push(`/actions/new?auditId=${audit.id}`)}
                    size="sm"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Nouvelle action
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {actionsError && (
                <Alert variant="destructive" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{actionsError}</AlertDescription>
                </Alert>
              )}

              {actionActionError && (
                <Alert variant="destructive" className="mb-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{actionActionError}</AlertDescription>
                </Alert>
              )}

              <ActionTable
                actions={actions}
                onView={handleViewAction}
                onEdit={canUpdateAction() ? handleEditAction : undefined}
                onDelete={canDeleteAction() ? handleDeleteAction : undefined}
                onComplete={canCompleteAction() ? handleCompleteAction : undefined}
                onStart={canUpdateAction() ? handleStartAction : undefined}
                onReopen={canUpdateAction() ? handleReopenAction : undefined}
                loading={actionsLoading || actionActionLoading}
                showAuditInfo={false}
                showObservationInfo={true}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Rapports</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun rapport</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Les rapports seront affichés ici une fois générés.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
