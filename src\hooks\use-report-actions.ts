"use client"

import { useState } from "react"
import { 
  CreateReportInput, 
  UpdateReportInput, 
  GenerateReportInput,
  ExportReportInput,
  PublishReportInput,
  ReportWithRelations 
} from "@/lib/validations/report"

interface UseReportActionsResult {
  loading: boolean
  error: string | null
  createReport: (data: CreateReportInput) => Promise<ReportWithRelations | null>
  updateReport: (id: string, data: UpdateReportInput) => Promise<ReportWithRelations | null>
  deleteReport: (id: string) => Promise<boolean>
  getReport: (id: string) => Promise<ReportWithRelations | null>
  generateReport: (data: GenerateReportInput) => Promise<ReportWithRelations | null>
  exportReport: (id: string, data: ExportReportInput) => Promise<Blob | null>
  publishReport: (id: string, data: PublishReportInput) => Promise<ReportWithRelations | null>
  clearError: () => void
}

export function useReportActions(): UseReportActionsResult {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const clearError = () => setError(null)

  const createReport = async (data: CreateReportInput): Promise<ReportWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch("/api/reports", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la création du rapport")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la création du rapport")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la création du rapport:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const updateReport = async (id: string, data: UpdateReportInput): Promise<ReportWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/reports/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la mise à jour du rapport")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la mise à jour du rapport")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la mise à jour du rapport:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const deleteReport = async (id: string): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/reports/${id}`, {
        method: "DELETE",
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la suppression du rapport")
      }

      return result.success
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la suppression du rapport:", err)
      return false
    } finally {
      setLoading(false)
    }
  }

  const getReport = async (id: string): Promise<ReportWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/reports/${id}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la récupération du rapport")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la récupération du rapport")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la récupération du rapport:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const generateReport = async (data: GenerateReportInput): Promise<ReportWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch("/api/reports/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la génération du rapport")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la génération du rapport")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la génération du rapport:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const exportReport = async (id: string, data: ExportReportInput): Promise<Blob | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/reports/${id}/export`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const result = await response.json()
        throw new Error(result.error || "Erreur lors de l'export du rapport")
      }

      const blob = await response.blob()
      return blob
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de l'export du rapport:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const publishReport = async (id: string, data: PublishReportInput): Promise<ReportWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/reports/${id}/publish`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la publication du rapport")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la publication du rapport")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la publication du rapport:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  return {
    loading,
    error,
    createReport,
    updateReport,
    deleteReport,
    getReport,
    generateReport,
    exportReport,
    publishReport,
    clearError
  }
}
