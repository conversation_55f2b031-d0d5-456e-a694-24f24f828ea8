import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ActionService } from "@/lib/services/action-service"
import { completeActionSchema } from "@/lib/validations/action"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"
import { z } from "zod"

/**
 * PUT /api/actions/[id]/complete - Marquer une action comme terminée
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier que l'action existe
      const existingAction = await ActionService.getActionById(params.id)
      if (!existingAction) {
        return NextResponse.json(
          { success: false, error: "Action non trouvée" },
          { status: 404 }
        )
      }

      // Vérifier les permissions - l'assigné peut marquer sa propre action comme terminée
      const canUpdate = hasPermission(user.role as UserRole, 'actions', 'update')
      const isAssignee = existingAction.assigneeId === user.id

      if (!canUpdate && !isAssignee) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      const body = await req.json()
      const validatedData = completeActionSchema.parse(body)

      const action = await ActionService.completeAction(params.id, validatedData)

      return NextResponse.json({
        success: true,
        data: action
      })

    } catch (error) {
      console.error("Erreur lors de la completion de l'action:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Données invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * DELETE /api/actions/[id]/complete - Rouvrir une action (annuler la completion)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'actions', 'update')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que l'action existe
      const existingAction = await ActionService.getActionById(params.id)
      if (!existingAction) {
        return NextResponse.json(
          { success: false, error: "Action non trouvée" },
          { status: 404 }
        )
      }

      const action = await ActionService.reopenAction(params.id)

      return NextResponse.json({
        success: true,
        data: action
      })

    } catch (error) {
      console.error("Erreur lors de la réouverture de l'action:", error)
      
      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
