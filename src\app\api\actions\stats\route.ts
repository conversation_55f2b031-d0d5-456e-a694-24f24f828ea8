import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ActionService } from "@/lib/services/action-service"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"

/**
 * GET /api/actions/stats - Obtenir les statistiques des actions
 */
export async function GET(request: NextRequest) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'actions', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      const { searchParams } = new URL(req.url)
      const auditId = searchParams.get('auditId') || undefined

      const stats = await ActionService.getActionStats(auditId)

      return NextResponse.json({
        success: true,
        data: stats
      })

    } catch (error) {
      console.error("Erreur lors de la récupération des statistiques des actions:", error)
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
