import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ActionStatusBadge } from "./action-status-badge"
import { ActionPriorityBadge } from "./action-priority-badge"
import { ActionWithRelations, getDaysUntilDue, isActionOverdue } from "@/lib/validations/action"
import { Eye, Edit, Trash2, CheckCircle, Play, RotateCcw, User, Calendar, FileText } from "lucide-react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

interface ActionCardProps {
  action: ActionWithRelations
  onView?: (action: ActionWithRelations) => void
  onEdit?: (action: ActionWithRelations) => void
  onDelete?: (action: ActionWithRelations) => void
  onComplete?: (action: ActionWithRelations) => void
  onStart?: (action: ActionWithRelations) => void
  onReopen?: (action: ActionWithRelations) => void
  showAuditInfo?: boolean
  showObservationInfo?: boolean
  className?: string
}

export function ActionCard({
  action,
  onView,
  onEdit,
  onDelete,
  onComplete,
  onStart,
  onReopen,
  showAuditInfo = true,
  showObservationInfo = true,
  className
}: ActionCardProps) {
  const daysUntilDue = getDaysUntilDue(action.dueDate)
  const overdue = isActionOverdue(action)

  const formatDate = (date: Date) => {
    return format(date, "dd/MM/yyyy", { locale: fr })
  }

  const formatDateTime = (date: Date) => {
    return format(date, "dd/MM/yyyy à HH:mm", { locale: fr })
  }

  const getDueDateColor = () => {
    if (overdue) return "text-red-600"
    if (daysUntilDue <= 3) return "text-orange-600"
    if (daysUntilDue <= 7) return "text-yellow-600"
    return "text-gray-600"
  }

  const getDueDateText = () => {
    if (overdue) {
      const daysPast = Math.abs(daysUntilDue)
      return `En retard de ${daysPast} jour${daysPast > 1 ? 's' : ''}`
    }
    if (daysUntilDue === 0) return "Échéance aujourd'hui"
    if (daysUntilDue === 1) return "Échéance demain"
    return `Dans ${daysUntilDue} jour${daysUntilDue > 1 ? 's' : ''}`
  }

  return (
    <Card className={`hover:shadow-md transition-shadow ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg font-semibold line-clamp-2">
              {action.title}
            </CardTitle>
            <CardDescription className="mt-1 line-clamp-2">
              {action.description}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2 ml-4">
            <ActionPriorityBadge priority={action.priority} />
            <ActionStatusBadge status={action.status} />
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Informations principales */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-gray-500" />
            <div>
              <p className="text-gray-500">Assigné à</p>
              <p className="font-medium">{action.assignee.name || action.assignee.email}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <div>
              <p className="text-gray-500">Échéance</p>
              <p className={`font-medium ${getDueDateColor()}`}>
                {formatDate(action.dueDate)}
              </p>
              <p className={`text-xs ${getDueDateColor()}`}>
                {getDueDateText()}
              </p>
            </div>
          </div>
        </div>

        {/* Informations contextuelles */}
        {(showAuditInfo || showObservationInfo) && (
          <div className="space-y-2 pt-2 border-t">
            {showAuditInfo && (
              <div className="flex items-center gap-2 text-sm">
                <FileText className="h-4 w-4 text-gray-500" />
                <span className="text-gray-500">Audit:</span>
                <span className="font-medium">{action.audit.title}</span>
              </div>
            )}
            
            {showObservationInfo && action.observation && (
              <div className="flex items-center gap-2 text-sm">
                <FileText className="h-4 w-4 text-gray-500" />
                <span className="text-gray-500">Observation:</span>
                <span className="font-medium">{action.observation.title}</span>
              </div>
            )}
          </div>
        )}

        {/* Dates système */}
        <div className="text-xs text-gray-500 pt-2 border-t">
          <div className="flex justify-between">
            <span>Créé le {formatDateTime(action.createdAt)}</span>
            {action.completedAt && (
              <span>Terminé le {formatDateTime(action.completedAt)}</span>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center gap-2">
            {onView && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onView(action)}
              >
                <Eye className="h-4 w-4 mr-1" />
                Voir
              </Button>
            )}
            
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(action)}
              >
                <Edit className="h-4 w-4 mr-1" />
                Modifier
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2">
            {onStart && action.status === "PENDING" && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onStart(action)}
                className="text-blue-600 border-blue-200 hover:bg-blue-50"
              >
                <Play className="h-4 w-4 mr-1" />
                Démarrer
              </Button>
            )}
            
            {onComplete && (action.status === "PENDING" || action.status === "IN_PROGRESS" || action.status === "OVERDUE") && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onComplete(action)}
                className="text-green-600 border-green-200 hover:bg-green-50"
              >
                <CheckCircle className="h-4 w-4 mr-1" />
                Terminer
              </Button>
            )}
            
            {onReopen && (action.status === "COMPLETED" || action.status === "CANCELLED") && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onReopen(action)}
                className="text-orange-600 border-orange-200 hover:bg-orange-50"
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Rouvrir
              </Button>
            )}
            
            {onDelete && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(action)}
                className="text-red-600 border-red-200 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Supprimer
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
