"use client"

import { useSession } from "@/lib/auth/client"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"

// Hook pour vérifier les permissions d'audit
export function useAuditPermissions(audit?: any) {
  const { data: session } = useSession()

  const checkPermission = (permission: string): boolean => {
    if (!session?.user) return false

    const [resource, action] = permission.split(':')
    const userRole = session.user.role as UserRole

    return hasPermission(userRole, resource, action)
  }

  return {
    canRead: () => checkPermission("audits:read"),
    canCreate: () => checkPermission("audits:create"),
    canUpdate: (auditToCheck?: any) => {
      if (!checkPermission("audits:update")) return false

      // Vérifications supplémentaires selon l'audit spécifique
      const targetAudit = auditToCheck || audit
      if (!targetAudit || !session?.user) return true

      // Les super admins peuvent tout modifier
      if (session.user.role === "SUPER_ADMIN") return true

      // Les autres peuvent modifier leurs propres audits ou ceux de leur organisation
      return targetAudit.creatorId === session.user.id ||
             targetAudit.organizationId === session.user.organizationId ||
             targetAudit.auditors?.some((a: any) => a.userId === session.user.id)
    },
    canDelete: (auditToCheck?: any) => {
      if (!checkPermission("audits:delete")) return false

      // Vérifications supplémentaires selon l'audit spécifique
      const targetAudit = auditToCheck || audit
      if (!targetAudit || !session?.user) return true

      // Les super admins peuvent tout supprimer
      if (session.user.role === "SUPER_ADMIN") return true

      // Les autres peuvent supprimer leurs propres audits ou ceux de leur organisation
      return targetAudit.creatorId === session.user.id ||
             targetAudit.organizationId === session.user.organizationId
    },
    canAssignAuditors: () => checkPermission("audits:update"),
    canCreateObservation: () => checkPermission("observations:create"),
    canCreateAction: () => checkPermission("audits:update"),
    canGenerateReport: () => checkPermission("reports:create"),
    user: session?.user || null
  }
}
