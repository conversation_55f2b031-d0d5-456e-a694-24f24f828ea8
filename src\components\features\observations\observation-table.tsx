"use client"

import { useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { ObservationSeverityBadge } from "./observation-severity-badge"
import { ObservationStatusBadge } from "./observation-status-badge"
import { ObservationWithRelations } from "@/lib/validations/observation"
import { Eye, Edit, Trash2, MoreHorizontal, CheckCircle, XCircle, RotateCcw, FileText, Plus } from "lucide-react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface ObservationTableProps {
  observations: ObservationWithRelations[]
  onView?: (observation: ObservationWithRelations) => void
  onEdit?: (observation: ObservationWithRelations) => void
  onDelete?: (observation: ObservationWithRelations) => void
  onResolve?: (observation: ObservationWithRelations) => void
  onClose?: (observation: ObservationWithRelations) => void
  onReopen?: (observation: ObservationWithRelations) => void
  onCreateAction?: (observation: ObservationWithRelations) => void
  onSelectionChange?: (selectedIds: string[]) => void
  loading?: boolean
  showAuditInfo?: boolean
}

export function ObservationTable({
  observations,
  onView,
  onEdit,
  onDelete,
  onResolve,
  onClose,
  onReopen,
  onCreateAction,
  onSelectionChange,
  loading = false,
  showAuditInfo = true
}: ObservationTableProps) {
  const [selectedIds, setSelectedIds] = useState<string[]>([])

  const formatDate = (date: Date) => {
    return format(date, "dd/MM/yyyy", { locale: fr })
  }

  const handleSelectAll = (checked: boolean) => {
    const newSelection = checked ? observations.map(obs => obs.id) : []
    setSelectedIds(newSelection)
    onSelectionChange?.(newSelection)
  }

  const handleSelectOne = (observationId: string, checked: boolean) => {
    const newSelection = checked 
      ? [...selectedIds, observationId]
      : selectedIds.filter(id => id !== observationId)
    
    setSelectedIds(newSelection)
    onSelectionChange?.(newSelection)
  }

  const isAllSelected = observations.length > 0 && selectedIds.length === observations.length
  const isPartiallySelected = selectedIds.length > 0 && selectedIds.length < observations.length

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-100 rounded animate-pulse" />
        ))}
      </div>
    )
  }

  if (observations.length === 0) {
    return (
      <div className="text-center py-12">
        <FileText className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune observation</h3>
        <p className="mt-1 text-sm text-gray-500">
          Commencez par créer votre première observation.
        </p>
      </div>
    )
  }

  return (
    <div className="border rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={isAllSelected}
                indeterminate={isPartiallySelected}
                onCheckedChange={handleSelectAll}
              />
            </TableHead>
            <TableHead>Titre</TableHead>
            {showAuditInfo && <TableHead>Audit</TableHead>}
            <TableHead>Sévérité</TableHead>
            <TableHead>Statut</TableHead>
            <TableHead>Actions</TableHead>
            <TableHead>Date</TableHead>
            <TableHead className="w-12"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {observations.map((observation) => {
            const isSelected = selectedIds.includes(observation.id)
            const canResolve = observation.status === "OPEN" || observation.status === "IN_PROGRESS"
            const canClose = observation.status === "RESOLVED"
            const canReopen = observation.status === "RESOLVED" || observation.status === "CLOSED"
            const canEdit = observation.status !== "CLOSED" && observation.status !== "REJECTED"
            const canDelete = observation.status === "OPEN"

            return (
              <TableRow 
                key={observation.id}
                className={isSelected ? "bg-blue-50" : ""}
              >
                <TableCell>
                  <Checkbox
                    checked={isSelected}
                    onCheckedChange={(checked) => handleSelectOne(observation.id, checked as boolean)}
                  />
                </TableCell>
                
                <TableCell>
                  <div>
                    <div className="font-medium text-gray-900 line-clamp-1">{observation.title}</div>
                    <div className="text-sm text-gray-500 line-clamp-1 max-w-xs">
                      {observation.description}
                    </div>
                  </div>
                </TableCell>
                
                {showAuditInfo && (
                  <TableCell>
                    <div className="text-sm text-gray-900 line-clamp-1 max-w-32">
                      {observation.audit.title}
                    </div>
                    <div className="text-xs text-gray-500">
                      {observation.audit.organization.name}
                    </div>
                  </TableCell>
                )}
                
                <TableCell>
                  <ObservationSeverityBadge severity={observation.severity} />
                </TableCell>
                
                <TableCell>
                  <ObservationStatusBadge status={observation.status} />
                </TableCell>
                
                <TableCell>
                  <div className="text-sm text-gray-900">
                    {observation._count.actions}
                  </div>
                  <div className="text-xs text-gray-500">
                    action{observation._count.actions > 1 ? 's' : ''}
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="text-sm text-gray-900">
                    {formatDate(observation.createdAt)}
                  </div>
                </TableCell>
                
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {onView && (
                        <DropdownMenuItem onClick={() => onView(observation)}>
                          <Eye className="mr-2 h-4 w-4" />
                          Voir les détails
                        </DropdownMenuItem>
                      )}
                      
                      {onEdit && canEdit && (
                        <DropdownMenuItem onClick={() => onEdit(observation)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Modifier
                        </DropdownMenuItem>
                      )}

                      {onCreateAction && (
                        <DropdownMenuItem onClick={() => onCreateAction(observation)}>
                          <Plus className="mr-2 h-4 w-4" />
                          Créer une action
                        </DropdownMenuItem>
                      )}

                      <DropdownMenuSeparator />

                      {onResolve && canResolve && (
                        <DropdownMenuItem 
                          onClick={() => onResolve(observation)}
                          className="text-green-600"
                        >
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Résoudre
                        </DropdownMenuItem>
                      )}

                      {onClose && canClose && (
                        <DropdownMenuItem 
                          onClick={() => onClose(observation)}
                          className="text-blue-600"
                        >
                          <XCircle className="mr-2 h-4 w-4" />
                          Fermer
                        </DropdownMenuItem>
                      )}

                      {onReopen && canReopen && observation.status !== "OPEN" && (
                        <DropdownMenuItem 
                          onClick={() => onReopen(observation)}
                          className="text-orange-600"
                        >
                          <RotateCcw className="mr-2 h-4 w-4" />
                          Rouvrir
                        </DropdownMenuItem>
                      )}
                      
                      {onDelete && canDelete && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => onDelete(observation)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Supprimer
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}
