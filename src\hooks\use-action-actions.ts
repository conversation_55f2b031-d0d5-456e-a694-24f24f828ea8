"use client"

import { useState } from "react"
import { CreateActionInput, UpdateActionInput, ActionWithRelations, AssignActionInput, CompleteActionInput } from "@/lib/validations/action"

interface UseActionActionsResult {
  loading: boolean
  error: string | null
  createAction: (data: CreateActionInput) => Promise<ActionWithRelations | null>
  updateAction: (id: string, data: UpdateActionInput) => Promise<ActionWithRelations | null>
  deleteAction: (id: string) => Promise<boolean>
  getAction: (id: string) => Promise<ActionWithRelations | null>
  assignAction: (id: string, data: AssignActionInput) => Promise<ActionWithRelations | null>
  completeAction: (id: string, data?: CompleteActionInput) => Promise<ActionWithRelations | null>
  startAction: (id: string) => Promise<ActionWithRelations | null>
  cancelAction: (id: string) => Promise<ActionWithRelations | null>
  reopenAction: (id: string) => Promise<ActionWithRelations | null>
  clearError: () => void
}

export function useActionActions(): UseActionActionsResult {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const clearError = () => setError(null)

  const createAction = async (data: CreateActionInput): Promise<ActionWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch("/api/actions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la création de l'action")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la création de l'action")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la création de l'action:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const updateAction = async (id: string, data: UpdateActionInput): Promise<ActionWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/actions/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la mise à jour de l'action")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la mise à jour de l'action")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la mise à jour de l'action:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const deleteAction = async (id: string): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/actions/${id}`, {
        method: "DELETE",
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la suppression de l'action")
      }

      return result.success
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la suppression de l'action:", err)
      return false
    } finally {
      setLoading(false)
    }
  }

  const getAction = async (id: string): Promise<ActionWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/actions/${id}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la récupération de l'action")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la récupération de l'action")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la récupération de l'action:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const assignAction = async (id: string, data: AssignActionInput): Promise<ActionWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/actions/${id}/assign`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de l'assignation de l'action")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de l'assignation de l'action")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de l'assignation de l'action:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const completeAction = async (id: string, data?: CompleteActionInput): Promise<ActionWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/actions/${id}/complete`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data || {}),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la completion de l'action")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la completion de l'action")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la completion de l'action:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const startAction = async (id: string): Promise<ActionWithRelations | null> => {
    return updateAction(id, { status: "IN_PROGRESS" })
  }

  const cancelAction = async (id: string): Promise<ActionWithRelations | null> => {
    return updateAction(id, { status: "CANCELLED" })
  }

  const reopenAction = async (id: string): Promise<ActionWithRelations | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/actions/${id}/complete`, {
        method: "DELETE",
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors de la réouverture de l'action")
      }

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error || "Erreur lors de la réouverture de l'action")
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la réouverture de l'action:", err)
      return null
    } finally {
      setLoading(false)
    }
  }

  return {
    loading,
    error,
    createAction,
    updateAction,
    deleteAction,
    getAction,
    assignAction,
    completeAction,
    startAction,
    cancelAction,
    reopenAction,
    clearError
  }
}
