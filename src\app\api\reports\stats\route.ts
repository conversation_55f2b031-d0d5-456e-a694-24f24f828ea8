import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ReportService } from "@/lib/services/report-service"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"

/**
 * GET /api/reports/stats - Obtenir les statistiques des rapports
 */
export async function GET(request: NextRequest) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'reports', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Si l'utilisateur n'est pas admin, filtrer par son organisation
      const organizationId = user.role !== "SUPER_ADMIN" && user.organizationId 
        ? user.organizationId 
        : undefined

      const stats = await ReportService.getReportStats(organizationId)

      return NextResponse.json({
        success: true,
        data: stats
      })

    } catch (error) {
      console.error("Erreur lors de la récupération des statistiques des rapports:", error)
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
