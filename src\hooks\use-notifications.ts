"use client"

import { useState, useEffect, useCallback } from "react"
import { NotificationData } from "@/lib/services/notification-service"

interface UseNotificationsResult {
  notifications: NotificationData[]
  unreadCount: number
  loading: boolean
  error: string | null
  markAsRead: (notificationId: string) => Promise<void>
  markAllAsRead: () => Promise<void>
  refetch: () => Promise<void>
}

export function useNotifications(userId: string): UseNotificationsResult {
  const [notifications, setNotifications] = useState<NotificationData[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Simuler des notifications pour la démo
  const mockNotifications: NotificationData[] = [
    {
      id: "1",
      type: "ACTION_OVERDUE",
      title: "Action en retard",
      message: "L'action \"Corriger la non-conformité documentaire\" est en retard de 2 jours",
      actionId: "action-1",
      userId,
      read: false,
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
    },
    {
      id: "2",
      type: "ACTION_DUE_SOON",
      title: "Échéance proche",
      message: "L'action \"Mettre à jour les procédures\" arrive à échéance demain",
      actionId: "action-2",
      userId,
      read: false,
      createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
    },
    {
      id: "3",
      type: "ACTION_ASSIGNED",
      title: "Nouvelle action assignée",
      message: "Une nouvelle action \"Formation du personnel\" vous a été assignée",
      actionId: "action-3",
      userId,
      read: true,
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
    },
    {
      id: "4",
      type: "ACTION_COMPLETED",
      title: "Action terminée",
      message: "L'action \"Révision des processus\" a été marquée comme terminée",
      actionId: "action-4",
      userId,
      read: true,
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
    }
  ]

  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Dans une vraie application, on ferait un appel API
      // const response = await fetch(`/api/notifications?userId=${userId}`)
      // const result = await response.json()
      
      // Pour la démo, on utilise les données mockées
      await new Promise(resolve => setTimeout(resolve, 500)) // Simuler un délai réseau
      
      setNotifications(mockNotifications)
      setUnreadCount(mockNotifications.filter(n => !n.read).length)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors de la récupération des notifications:", err)
    } finally {
      setLoading(false)
    }
  }, [userId])

  const markAsRead = async (notificationId: string): Promise<void> => {
    try {
      // Dans une vraie application, on ferait un appel API
      // await fetch(`/api/notifications/${notificationId}/read`, { method: 'PUT' })
      
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId ? { ...n, read: true } : n
        )
      )
      setUnreadCount(prev => Math.max(0, prev - 1))

    } catch (err) {
      console.error("Erreur lors du marquage de la notification:", err)
      throw err
    }
  }

  const markAllAsRead = async (): Promise<void> => {
    try {
      // Dans une vraie application, on ferait un appel API
      // await fetch(`/api/notifications/mark-all-read`, { 
      //   method: 'PUT',
      //   body: JSON.stringify({ userId })
      // })
      
      setNotifications(prev => prev.map(n => ({ ...n, read: true })))
      setUnreadCount(0)

    } catch (err) {
      console.error("Erreur lors du marquage de toutes les notifications:", err)
      throw err
    }
  }

  // Charger les notifications au montage du composant
  useEffect(() => {
    if (userId) {
      fetchNotifications()
    }
  }, [userId, fetchNotifications])

  // Polling pour les nouvelles notifications (toutes les 30 secondes)
  useEffect(() => {
    if (!userId) return

    const interval = setInterval(() => {
      fetchNotifications()
    }, 30000) // 30 secondes

    return () => clearInterval(interval)
  }, [userId, fetchNotifications])

  return {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    refetch: fetchNotifications
  }
}

// Hook pour obtenir uniquement le nombre de notifications non lues
export function useUnreadNotificationsCount(userId: string): {
  unreadCount: number
  loading: boolean
  error: string | null
} {
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchUnreadCount = async () => {
      try {
        setLoading(true)
        setError(null)

        // Dans une vraie application, on ferait un appel API
        // const response = await fetch(`/api/notifications/unread-count?userId=${userId}`)
        // const result = await response.json()
        
        // Pour la démo, on simule
        await new Promise(resolve => setTimeout(resolve, 200))
        setUnreadCount(2) // Simuler 2 notifications non lues

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
        setError(errorMessage)
      } finally {
        setLoading(false)
      }
    }

    if (userId) {
      fetchUnreadCount()
      
      // Polling toutes les minutes pour le compteur
      const interval = setInterval(fetchUnreadCount, 60000)
      return () => clearInterval(interval)
    }
  }, [userId])

  return { unreadCount, loading, error }
}

// Hook pour déclencher manuellement les vérifications de notifications
export function useNotificationChecks() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const triggerChecks = async (): Promise<boolean> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/notifications/check', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_CRON_SECRET || 'default-secret'}`
        }
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Erreur lors des vérifications")
      }

      return result.success

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erreur inconnue"
      setError(errorMessage)
      console.error("Erreur lors du déclenchement des vérifications:", err)
      return false
    } finally {
      setLoading(false)
    }
  }

  return {
    triggerChecks,
    loading,
    error
  }
}
