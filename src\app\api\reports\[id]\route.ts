import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ReportService } from "@/lib/services/report-service"
import { updateReportSchema } from "@/lib/validations/report"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"
import { z } from "zod"

/**
 * GET /api/reports/[id] - Obtenir un rapport par ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'reports', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      const report = await ReportService.getReportById(params.id)

      if (!report) {
        return NextResponse.json(
          { success: false, error: "Rapport non trouvé" },
          { status: 404 }
        )
      }

      // Vérifier que l'utilisateur a accès à ce rapport
      if (user.role !== "SUPER_ADMIN" && user.organizationId !== report.organizationId) {
        return NextResponse.json(
          { success: false, error: "Accès non autorisé" },
          { status: 403 }
        )
      }

      // Si l'utilisateur est un auditeur simple, ne voir que ses rapports
      if (user.role === "AUDITOR" && report.creatorId !== user.id) {
        return NextResponse.json(
          { success: false, error: "Accès non autorisé" },
          { status: 403 }
        )
      }

      return NextResponse.json({
        success: true,
        data: report
      })

    } catch (error) {
      console.error("Erreur lors de la récupération du rapport:", error)
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * PUT /api/reports/[id] - Mettre à jour un rapport
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'reports', 'update')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que le rapport existe et que l'utilisateur y a accès
      const existingReport = await ReportService.getReportById(params.id)
      
      if (!existingReport) {
        return NextResponse.json(
          { success: false, error: "Rapport non trouvé" },
          { status: 404 }
        )
      }

      // Vérifier l'accès organisationnel
      if (user.role !== "SUPER_ADMIN" && user.organizationId !== existingReport.organizationId) {
        return NextResponse.json(
          { success: false, error: "Accès non autorisé" },
          { status: 403 }
        )
      }

      // Si l'utilisateur est un auditeur simple, ne peut modifier que ses rapports
      if (user.role === "AUDITOR" && existingReport.creatorId !== user.id) {
        return NextResponse.json(
          { success: false, error: "Accès non autorisé" },
          { status: 403 }
        )
      }

      const body = await req.json()
      const validatedData = updateReportSchema.parse(body)

      const updatedReport = await ReportService.updateReport(params.id, validatedData)

      return NextResponse.json({
        success: true,
        data: updatedReport
      })

    } catch (error) {
      console.error("Erreur lors de la mise à jour du rapport:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Données invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * DELETE /api/reports/[id] - Supprimer un rapport
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'reports', 'delete')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que le rapport existe et que l'utilisateur y a accès
      const existingReport = await ReportService.getReportById(params.id)
      
      if (!existingReport) {
        return NextResponse.json(
          { success: false, error: "Rapport non trouvé" },
          { status: 404 }
        )
      }

      // Vérifier l'accès organisationnel
      if (user.role !== "SUPER_ADMIN" && user.organizationId !== existingReport.organizationId) {
        return NextResponse.json(
          { success: false, error: "Accès non autorisé" },
          { status: 403 }
        )
      }

      // Seuls les admins et managers peuvent supprimer des rapports
      if (!["SUPER_ADMIN", "ADMIN", "MANAGER"].includes(user.role)) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes pour supprimer" },
          { status: 403 }
        )
      }

      await ReportService.deleteReport(params.id)

      return NextResponse.json({
        success: true,
        message: "Rapport supprimé avec succès"
      })

    } catch (error) {
      console.error("Erreur lors de la suppression du rapport:", error)
      
      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
