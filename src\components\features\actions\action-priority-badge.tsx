import { Badge } from "@/components/ui/badge"
import { ActionPriority, getActionPriorityLabel, getActionPriorityColor } from "@/lib/validations/action"
import { ArrowDown, Minus, ArrowUp, AlertTriangle } from "lucide-react"

interface ActionPriorityBadgeProps {
  priority: ActionPriority
  className?: string
}

export function ActionPriorityBadge({ priority, className }: ActionPriorityBadgeProps) {
  const label = getActionPriorityLabel(priority)
  const colorClasses = getActionPriorityColor(priority)

  const getIcon = () => {
    switch (priority) {
      case ActionPriority.LOW:
        return <ArrowDown className="h-3 w-3 mr-1" />
      case ActionPriority.MEDIUM:
        return <Minus className="h-3 w-3 mr-1" />
      case ActionPriority.HIGH:
        return <ArrowUp className="h-3 w-3 mr-1" />
      case ActionPriority.CRITICAL:
        return <AlertTriangle className="h-3 w-3 mr-1" />
      default:
        return null
    }
  }

  return (
    <Badge 
      variant="outline" 
      className={`${colorClasses} ${className}`}
    >
      {getIcon()}
      {label}
    </Badge>
  )
}
