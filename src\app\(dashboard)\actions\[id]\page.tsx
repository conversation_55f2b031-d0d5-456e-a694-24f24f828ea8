"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ActionStatusBadge, ActionPriorityBadge } from "@/components/features/actions"
import { useActionActions } from "@/hooks/use-action-actions"
import { useActionPermissions } from "@/hooks/use-action-permissions"
import { ActionWithRelations, getDaysUntilDue, isActionOverdue } from "@/lib/validations/action"
import { ArrowLeft, Edit, CheckCircle, Play, RotateCcw, Trash2, User, Calendar, FileText, AlertTriangle, Clock } from "lucide-react"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

interface ActionDetailPageProps {
  params: {
    id: string
  }
}

export default function ActionDetailPage({ params }: ActionDetailPageProps) {
  const router = useRouter()
  const [action, setAction] = useState<ActionWithRelations | null>(null)
  
  const {
    loading,
    error,
    getAction,
    completeAction,
    startAction,
    reopenAction,
    deleteAction,
    clearError
  } = useActionActions()

  const {
    canUpdate,
    canDelete,
    canComplete,
    canReopen
  } = useActionPermissions(action || undefined)

  useEffect(() => {
    const fetchAction = async () => {
      const actionData = await getAction(params.id)
      if (actionData) {
        setAction(actionData)
      }
    }

    fetchAction()
  }, [params.id, getAction])

  const handleComplete = async () => {
    if (!action) return
    if (confirm(`Marquer l'action "${action.title}" comme terminée ?`)) {
      const completed = await completeAction(action.id)
      if (completed) {
        setAction(completed)
      }
    }
  }

  const handleStart = async () => {
    if (!action) return
    if (confirm(`Démarrer l'action "${action.title}" ?`)) {
      const started = await startAction(action.id)
      if (started) {
        setAction(started)
      }
    }
  }

  const handleReopen = async () => {
    if (!action) return
    if (confirm(`Rouvrir l'action "${action.title}" ?`)) {
      const reopened = await reopenAction(action.id)
      if (reopened) {
        setAction(reopened)
      }
    }
  }

  const handleDelete = async () => {
    if (!action) return
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'action "${action.title}" ?`)) {
      const success = await deleteAction(action.id)
      if (success) {
        router.push("/actions")
      }
    }
  }

  const formatDate = (date: Date) => {
    return format(date, "dd MMMM yyyy", { locale: fr })
  }

  const formatDateTime = (date: Date) => {
    return format(date, "dd/MM/yyyy à HH:mm", { locale: fr })
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="h-64 bg-gray-200 rounded animate-pulse" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-2"
            >
              Fermer
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!action) {
    return (
      <div className="space-y-6">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Action non trouvée
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  const daysUntilDue = getDaysUntilDue(action.dueDate)
  const overdue = isActionOverdue(action)

  const getDueDateColor = () => {
    if (overdue) return "text-red-600"
    if (daysUntilDue <= 3) return "text-orange-600"
    if (daysUntilDue <= 7) return "text-yellow-600"
    return "text-gray-600"
  }

  const getDueDateText = () => {
    if (overdue) {
      const daysPast = Math.abs(daysUntilDue)
      return `En retard de ${daysPast} jour${daysPast > 1 ? 's' : ''}`
    }
    if (daysUntilDue === 0) return "Échéance aujourd'hui"
    if (daysUntilDue === 1) return "Échéance demain"
    return `Dans ${daysUntilDue} jour${daysUntilDue > 1 ? 's' : ''}`
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          
          <div>
            <h1 className="text-3xl font-bold magneto-title">{action.title}</h1>
            <p className="text-gray-600">
              Action corrective
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <ActionPriorityBadge priority={action.priority} />
          <ActionStatusBadge status={action.status} />
          {canUpdate(action) && (
            <Button
              variant="outline"
              onClick={() => router.push(`/actions/${action.id}/edit`)}
            >
              <Edit className="h-4 w-4 mr-2" />
              Modifier
            </Button>
          )}
        </div>
      </div>

      {/* Informations principales */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 whitespace-pre-wrap">{action.description}</p>
            </CardContent>
          </Card>

          {/* Contexte */}
          <Card>
            <CardHeader>
              <CardTitle>Contexte</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-gray-500" />
                <span className="text-gray-500">Audit:</span>
                <Button
                  variant="link"
                  className="p-0 h-auto font-medium"
                  onClick={() => router.push(`/audits/${action.audit.id}`)}
                >
                  {action.audit.title}
                </Button>
              </div>
              
              {action.observation && (
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-500">Observation:</span>
                  <Button
                    variant="link"
                    className="p-0 h-auto font-medium"
                    onClick={() => router.push(`/observations/${action.observation!.id}`)}
                  >
                    {action.observation.title}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          {/* Informations clés */}
          <Card>
            <CardHeader>
              <CardTitle>Informations clés</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Assigné à</p>
                <div className="flex items-center gap-2 mt-1">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">{action.assignee.name || action.assignee.email}</span>
                </div>
              </div>
              
              <div>
                <p className="text-sm text-gray-500">Échéance</p>
                <div className="flex items-center gap-2 mt-1">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className={`font-medium ${getDueDateColor()}`}>
                      {formatDate(action.dueDate)}
                    </p>
                    <p className={`text-sm ${getDueDateColor()}`}>
                      {getDueDateText()}
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <p className="text-sm text-gray-500">Créé par</p>
                <div className="flex items-center gap-2 mt-1">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">{action.creator.name || action.creator.email}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions rapides */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {action.status === "PENDING" && canUpdate(action) && (
                <Button
                  onClick={handleStart}
                  className="w-full text-blue-600 border-blue-200 hover:bg-blue-50"
                  variant="outline"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Démarrer l'action
                </Button>
              )}
              
              {(action.status === "PENDING" || action.status === "IN_PROGRESS" || action.status === "OVERDUE") && canComplete(action) && (
                <Button
                  onClick={handleComplete}
                  className="w-full text-green-600 border-green-200 hover:bg-green-50"
                  variant="outline"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Marquer comme terminée
                </Button>
              )}
              
              {(action.status === "COMPLETED" || action.status === "CANCELLED") && canReopen(action) && (
                <Button
                  onClick={handleReopen}
                  className="w-full text-orange-600 border-orange-200 hover:bg-orange-50"
                  variant="outline"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Rouvrir l'action
                </Button>
              )}
              
              {canDelete(action) && (
                <Button
                  onClick={handleDelete}
                  className="w-full text-red-600 border-red-200 hover:bg-red-50"
                  variant="outline"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Supprimer l'action
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Informations système */}
          <Card>
            <CardHeader>
              <CardTitle>Informations système</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div>
                  <p className="text-gray-500">Créé le</p>
                  <p className="font-medium">{formatDateTime(action.createdAt)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Modifié le</p>
                  <p className="font-medium">{formatDateTime(action.updatedAt)}</p>
                </div>
                {action.completedAt && (
                  <div>
                    <p className="text-gray-500">Terminé le</p>
                    <p className="font-medium">{formatDateTime(action.completedAt)}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
