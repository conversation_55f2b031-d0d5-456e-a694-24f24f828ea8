"use client"

import { useSession } from "@/lib/auth/client"
import { useAuditPermissions } from "@/hooks/use-audit-permissions"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle, User, Shield } from "lucide-react"

export default function TestAuditPermissionsPage() {
  const { data: session, isLoading } = useSession()
  const {
    canRead,
    canCreate,
    canUpdate,
    canDelete,
    canAssignAuditors,
    canCreateObservation,
    canCreateAction,
    canGenerateReport,
    user
  } = useAuditPermissions()

  if (isLoading) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold magneto-title">Test des permissions d'audit</h1>
        <Card>
          <CardContent className="pt-6">
            <p>Chargement...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold magneto-title">Test des permissions d'audit</h1>
        <p className="text-gray-600">
          Vérification des permissions pour les audits
        </p>
      </div>

      {/* État de la session */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            État de la session
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {session?.user ? (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium">Connecté :</span>
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Oui
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Email :</span>
                <span className="text-sm">{session.user.email}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Nom :</span>
                <span className="text-sm">{session.user.name || "Non défini"}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Rôle :</span>
                <Badge variant="outline">{session.user.role}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">ID :</span>
                <span className="text-sm font-mono">{session.user.id}</span>
              </div>
            </div>
          ) : (
            <div className="text-center">
              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                <XCircle className="h-3 w-3 mr-1" />
                Non connecté
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Permissions d'audit */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Permissions d'audit
          </CardTitle>
          <CardDescription>
            Vérification des permissions spécifiques aux audits
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <span className="font-medium">Lire les audits</span>
              <Badge variant={canRead() ? "default" : "secondary"}>
                {canRead() ? (
                  <><CheckCircle className="h-3 w-3 mr-1" />Autorisé</>
                ) : (
                  <><XCircle className="h-3 w-3 mr-1" />Refusé</>
                )}
              </Badge>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <span className="font-medium">Créer des audits</span>
              <Badge variant={canCreate() ? "default" : "secondary"}>
                {canCreate() ? (
                  <><CheckCircle className="h-3 w-3 mr-1" />Autorisé</>
                ) : (
                  <><XCircle className="h-3 w-3 mr-1" />Refusé</>
                )}
              </Badge>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <span className="font-medium">Modifier les audits</span>
              <Badge variant={canUpdate() ? "default" : "secondary"}>
                {canUpdate() ? (
                  <><CheckCircle className="h-3 w-3 mr-1" />Autorisé</>
                ) : (
                  <><XCircle className="h-3 w-3 mr-1" />Refusé</>
                )}
              </Badge>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <span className="font-medium">Supprimer les audits</span>
              <Badge variant={canDelete() ? "default" : "secondary"}>
                {canDelete() ? (
                  <><CheckCircle className="h-3 w-3 mr-1" />Autorisé</>
                ) : (
                  <><XCircle className="h-3 w-3 mr-1" />Refusé</>
                )}
              </Badge>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <span className="font-medium">Assigner des auditeurs</span>
              <Badge variant={canAssignAuditors() ? "default" : "secondary"}>
                {canAssignAuditors() ? (
                  <><CheckCircle className="h-3 w-3 mr-1" />Autorisé</>
                ) : (
                  <><XCircle className="h-3 w-3 mr-1" />Refusé</>
                )}
              </Badge>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <span className="font-medium">Créer des observations</span>
              <Badge variant={canCreateObservation() ? "default" : "secondary"}>
                {canCreateObservation() ? (
                  <><CheckCircle className="h-3 w-3 mr-1" />Autorisé</>
                ) : (
                  <><XCircle className="h-3 w-3 mr-1" />Refusé</>
                )}
              </Badge>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <span className="font-medium">Créer des actions</span>
              <Badge variant={canCreateAction() ? "default" : "secondary"}>
                {canCreateAction() ? (
                  <><CheckCircle className="h-3 w-3 mr-1" />Autorisé</>
                ) : (
                  <><XCircle className="h-3 w-3 mr-1" />Refusé</>
                )}
              </Badge>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <span className="font-medium">Générer des rapports</span>
              <Badge variant={canGenerateReport() ? "default" : "secondary"}>
                {canGenerateReport() ? (
                  <><CheckCircle className="h-3 w-3 mr-1" />Autorisé</>
                ) : (
                  <><XCircle className="h-3 w-3 mr-1" />Refusé</>
                )}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions de test */}
      <Card>
        <CardHeader>
          <CardTitle>Actions de test</CardTitle>
          <CardDescription>
            Testez la navigation vers les pages d'audit
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button 
              onClick={() => window.open("/audits", "_blank")}
              className="magneto-button"
            >
              Aller à la page des audits
            </Button>
            <Button 
              onClick={() => window.open("/audits/new", "_blank")}
              disabled={!canCreate()}
              className="magneto-button"
            >
              Créer un nouvel audit
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Informations de débogage */}
      <Card>
        <CardHeader>
          <CardTitle>Informations de débogage</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="text-xs bg-gray-50 p-4 rounded-lg overflow-auto">
            {JSON.stringify({
              session: session ? {
                user: {
                  id: session.user.id,
                  email: session.user.email,
                  name: session.user.name,
                  role: session.user.role
                }
              } : null,
              permissions: {
                canRead: canRead(),
                canCreate: canCreate(),
                canUpdate: canUpdate(),
                canDelete: canDelete(),
                canAssignAuditors: canAssignAuditors(),
                canCreateObservation: canCreateObservation(),
                canCreateAction: canCreateAction(),
                canGenerateReport: canGenerateReport()
              }
            }, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  )
}
