import { z } from "zod"

// Enum pour les statuts de rapport
export const ReportStatus = {
  DRAFT: "DRAFT",
  IN_REVIEW: "IN_REVIEW",
  APPROVED: "APPROVED",
  PUBLISHED: "PUBLISHED",
  ARCHIVED: "ARCHIVED"
} as const

export type ReportStatusType = typeof ReportStatus[keyof typeof ReportStatus]

// Enum pour les types de rapport
export const ReportType = {
  AUDIT_SUMMARY: "AUDIT_SUMMARY",
  DETAILED_FINDINGS: "DETAILED_FINDINGS",
  ACTION_PLAN: "ACTION_PLAN",
  COMPLIANCE_REPORT: "COMPLIANCE_REPORT",
  EXECUTIVE_SUMMARY: "EXECUTIVE_SUMMARY"
} as const

export type ReportTypeType = typeof ReportType[keyof typeof ReportType]

// Enum pour les formats d'export
export const ExportFormat = {
  PDF: "PDF",
  WORD: "WORD",
  EXCEL: "EXCEL",
  HTML: "HTML"
} as const

export type ExportFormatType = typeof ExportFormat[keyof typeof ExportFormat]

// Schema de validation pour la création d'un rapport
export const createReportSchema = z.object({
  title: z.string().min(1, "Le titre est requis").max(200, "Le titre ne peut pas dépasser 200 caractères"),
  content: z.string().min(1, "Le contenu est requis"),
  auditId: z.string().min(1, "L'audit est requis"),
  organizationId: z.string().min(1, "L'organisation est requise"),
  type: z.enum([
    ReportType.AUDIT_SUMMARY,
    ReportType.DETAILED_FINDINGS,
    ReportType.ACTION_PLAN,
    ReportType.COMPLIANCE_REPORT,
    ReportType.EXECUTIVE_SUMMARY
  ]).default(ReportType.AUDIT_SUMMARY),
  includeObservations: z.boolean().default(true),
  includeActions: z.boolean().default(true),
  includeStatistics: z.boolean().default(true),
  template: z.string().optional(),
  metadata: z.record(z.any()).optional()
})

// Schema de validation pour la mise à jour d'un rapport
export const updateReportSchema = z.object({
  title: z.string().min(1, "Le titre est requis").max(200, "Le titre ne peut pas dépasser 200 caractères").optional(),
  content: z.string().min(1, "Le contenu est requis").optional(),
  status: z.enum([
    ReportStatus.DRAFT,
    ReportStatus.IN_REVIEW,
    ReportStatus.APPROVED,
    ReportStatus.PUBLISHED,
    ReportStatus.ARCHIVED
  ]).optional(),
  type: z.enum([
    ReportType.AUDIT_SUMMARY,
    ReportType.DETAILED_FINDINGS,
    ReportType.ACTION_PLAN,
    ReportType.COMPLIANCE_REPORT,
    ReportType.EXECUTIVE_SUMMARY
  ]).optional(),
  includeObservations: z.boolean().optional(),
  includeActions: z.boolean().optional(),
  includeStatistics: z.boolean().optional(),
  template: z.string().optional(),
  metadata: z.record(z.any()).optional()
})

// Schema pour les filtres de recherche
export const reportFiltersSchema = z.object({
  status: z.enum([
    ReportStatus.DRAFT,
    ReportStatus.IN_REVIEW,
    ReportStatus.APPROVED,
    ReportStatus.PUBLISHED,
    ReportStatus.ARCHIVED
  ]).optional(),
  type: z.enum([
    ReportType.AUDIT_SUMMARY,
    ReportType.DETAILED_FINDINGS,
    ReportType.ACTION_PLAN,
    ReportType.COMPLIANCE_REPORT,
    ReportType.EXECUTIVE_SUMMARY
  ]).optional(),
  organizationId: z.string().optional(),
  auditId: z.string().optional(),
  creatorId: z.string().optional(),
  createdFrom: z.string().datetime().optional(),
  createdTo: z.string().datetime().optional(),
  publishedFrom: z.string().datetime().optional(),
  publishedTo: z.string().datetime().optional(),
  search: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  sortBy: z.enum(["title", "status", "type", "createdAt", "publishedAt", "version"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc")
})

// Schema pour la génération automatique de rapport
export const generateReportSchema = z.object({
  auditId: z.string().min(1, "L'audit est requis"),
  type: z.enum([
    ReportType.AUDIT_SUMMARY,
    ReportType.DETAILED_FINDINGS,
    ReportType.ACTION_PLAN,
    ReportType.COMPLIANCE_REPORT,
    ReportType.EXECUTIVE_SUMMARY
  ]),
  title: z.string().optional(),
  includeObservations: z.boolean().default(true),
  includeActions: z.boolean().default(true),
  includeStatistics: z.boolean().default(true),
  template: z.string().optional(),
  autoPublish: z.boolean().default(false)
})

// Schema pour l'export de rapport
export const exportReportSchema = z.object({
  format: z.enum([
    ExportFormat.PDF,
    ExportFormat.WORD,
    ExportFormat.EXCEL,
    ExportFormat.HTML
  ]),
  includeAttachments: z.boolean().default(true),
  includeMetadata: z.boolean().default(false),
  template: z.string().optional(),
  options: z.record(z.any()).optional()
})

// Schema pour la publication de rapport
export const publishReportSchema = z.object({
  publishedAt: z.string().datetime().optional(),
  notifyStakeholders: z.boolean().default(true),
  distributionList: z.array(z.string()).optional(),
  message: z.string().optional()
})

// Types TypeScript inférés
export type CreateReportInput = z.infer<typeof createReportSchema>
export type UpdateReportInput = z.infer<typeof updateReportSchema>
export type ReportFilters = z.infer<typeof reportFiltersSchema>
export type GenerateReportInput = z.infer<typeof generateReportSchema>
export type ExportReportInput = z.infer<typeof exportReportSchema>
export type PublishReportInput = z.infer<typeof publishReportSchema>

// Type pour le rapport avec relations
export interface ReportWithRelations {
  id: string
  title: string
  content: string
  status: ReportStatusType
  type?: ReportTypeType
  version: number
  createdAt: Date
  updatedAt: Date
  publishedAt: Date | null
  organizationId: string
  auditId: string
  creatorId: string
  includeObservations?: boolean
  includeActions?: boolean
  includeStatistics?: boolean
  template?: string | null
  metadata?: Record<string, any> | null
  organization: {
    id: string
    name: string
  }
  audit: {
    id: string
    title: string
    status: string
    startDate: Date
    endDate: Date | null
  }
  creator: {
    id: string
    name: string | null
    email: string
  }
}

// Interface pour les statistiques de rapport
export interface ReportStats {
  total: number
  byStatus: Record<ReportStatusType, number>
  byType: Record<ReportTypeType, number>
  published: number
  draft: number
  averageGenerationTime: number
  totalExports: number
}

// Utilitaires pour les statuts
export const getReportStatusLabel = (status: ReportStatusType): string => {
  const labels: Record<ReportStatusType, string> = {
    [ReportStatus.DRAFT]: "Brouillon",
    [ReportStatus.IN_REVIEW]: "En révision",
    [ReportStatus.APPROVED]: "Approuvé",
    [ReportStatus.PUBLISHED]: "Publié",
    [ReportStatus.ARCHIVED]: "Archivé"
  }
  return labels[status]
}

export const getReportStatusColor = (status: ReportStatusType): string => {
  const colors: Record<ReportStatusType, string> = {
    [ReportStatus.DRAFT]: "bg-gray-100 text-gray-800",
    [ReportStatus.IN_REVIEW]: "bg-yellow-100 text-yellow-800",
    [ReportStatus.APPROVED]: "bg-blue-100 text-blue-800",
    [ReportStatus.PUBLISHED]: "bg-green-100 text-green-800",
    [ReportStatus.ARCHIVED]: "bg-red-100 text-red-800"
  }
  return colors[status]
}

export const getReportTypeLabel = (type: ReportTypeType): string => {
  const labels: Record<ReportTypeType, string> = {
    [ReportType.AUDIT_SUMMARY]: "Résumé d'audit",
    [ReportType.DETAILED_FINDINGS]: "Constats détaillés",
    [ReportType.ACTION_PLAN]: "Plan d'action",
    [ReportType.COMPLIANCE_REPORT]: "Rapport de conformité",
    [ReportType.EXECUTIVE_SUMMARY]: "Résumé exécutif"
  }
  return labels[type]
}

export const getExportFormatLabel = (format: ExportFormatType): string => {
  const labels: Record<ExportFormatType, string> = {
    [ExportFormat.PDF]: "PDF",
    [ExportFormat.WORD]: "Word",
    [ExportFormat.EXCEL]: "Excel",
    [ExportFormat.HTML]: "HTML"
  }
  return labels[format]
}

// Utilitaires pour la validation
export const isReportEditable = (status: ReportStatusType): boolean => {
  return [ReportStatus.DRAFT, ReportStatus.IN_REVIEW].includes(status)
}

export const isReportPublishable = (status: ReportStatusType): boolean => {
  return status === ReportStatus.APPROVED
}

export const canArchiveReport = (status: ReportStatusType): boolean => {
  return status === ReportStatus.PUBLISHED
}
