import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ActionService } from "@/lib/services/action-service"
import { AuditService } from "@/lib/services/audit-service"
import { createActionSchema } from "@/lib/validations/action"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"
import { z } from "zod"

/**
 * GET /api/audits/[id]/actions - Obtenir les actions d'un audit
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'actions', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que l'audit existe
      const audit = await AuditService.getAuditById(params.id)
      if (!audit) {
        return NextResponse.json(
          { success: false, error: "Audit non trouvé" },
          { status: 404 }
        )
      }

      // Vérifier si l'utilisateur peut voir cet audit
      if (user.role === "AUDITOR") {
        const isAssignedAuditor = audit.auditors.some(auditor => auditor.user.id === user.id)
        if (!isAssignedAuditor) {
          return NextResponse.json(
            { success: false, error: "Permissions insuffisantes" },
            { status: 403 }
          )
        }
      }

      const actions = await ActionService.getActionsByAudit(params.id)

      return NextResponse.json({
        success: true,
        data: actions
      })

    } catch (error) {
      console.error("Erreur lors de la récupération des actions de l'audit:", error)
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * POST /api/audits/[id]/actions - Créer une action pour un audit
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'actions', 'create')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que l'audit existe
      const audit = await AuditService.getAuditById(params.id)
      if (!audit) {
        return NextResponse.json(
          { success: false, error: "Audit non trouvé" },
          { status: 404 }
        )
      }

      // Vérifier si l'utilisateur peut créer des actions pour cet audit
      if (user.role === "AUDITOR") {
        const isAssignedAuditor = audit.auditors.some(auditor => auditor.user.id === user.id)
        if (!isAssignedAuditor) {
          return NextResponse.json(
            { success: false, error: "Permissions insuffisantes" },
            { status: 403 }
          )
        }
      }

      const body = await req.json()
      
      // Forcer l'auditId à celui de l'URL
      const validatedData = createActionSchema.parse({
        ...body,
        auditId: params.id
      })

      const action = await ActionService.createAction(validatedData, user.id)

      return NextResponse.json({
        success: true,
        data: action
      }, { status: 201 })

    } catch (error) {
      console.error("Erreur lors de la création de l'action:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Données invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
