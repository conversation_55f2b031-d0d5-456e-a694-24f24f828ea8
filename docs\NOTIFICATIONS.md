# Système de Notifications et Rappels d'Échéances

Ce document décrit le système de notifications et rappels d'échéances implémenté dans Magneto pour la gestion des actions correctives.

## Vue d'ensemble

Le système de notifications permet de :
- Alerter les utilisateurs sur les actions en retard
- Rappeler les échéances approchantes
- Notifier les assignations d'actions
- Confirmer la completion des actions

## Architecture

### Services

#### NotificationService (`src/lib/services/notification-service.ts`)
Service principal pour la gestion des notifications :
- `createNotification()` - Créer une nouvelle notification
- `checkOverdueActions()` - Vérifier les actions en retard
- `checkUpcomingDueActions()` - Vérifier les échéances approchantes
- `notifyActionAssigned()` - Notifier l'assignation d'une action
- `notifyActionCompleted()` - Notifier la completion d'une action
- `runNotificationChecks()` - Exécuter toutes les vérifications
- `cleanupOldNotifications()` - Nettoyer les anciennes notifications

### Composants UI

#### NotificationBell (`src/components/features/notifications/notification-bell.tsx`)
Composant de cloche de notifications dans la navbar :
- Affiche le nombre de notifications non lues
- Menu déroulant avec les notifications récentes
- Actions pour marquer comme lu

#### DueActionsDashboard (`src/components/features/notifications/due-actions-dashboard.tsx`)
Tableau de bord des échéances :
- Vue d'ensemble des actions critiques
- Statistiques par catégorie d'échéance
- Actions rapides (démarrer, terminer, etc.)

### Hooks React

#### useNotifications (`src/hooks/use-notifications.ts`)
Hook principal pour la gestion des notifications :
- `notifications` - Liste des notifications
- `unreadCount` - Nombre de notifications non lues
- `markAsRead()` - Marquer une notification comme lue
- `markAllAsRead()` - Marquer toutes les notifications comme lues

#### useNotificationChecks (`src/hooks/use-notifications.ts`)
Hook pour déclencher manuellement les vérifications :
- `triggerChecks()` - Déclencher les vérifications de notifications

### API Routes

#### `/api/notifications/check`
Endpoint pour déclencher les vérifications de notifications :
- `POST` - Exécuter les vérifications (pour cron jobs)
- `GET` - Obtenir le statut du service

## Types de Notifications

### ACTION_OVERDUE
- **Déclencheur** : Action dépassant sa date d'échéance
- **Destinataires** : Assigné + Créateur (si différent)
- **Fréquence** : Quotidienne

### ACTION_DUE_SOON
- **Déclencheur** : Action arrivant à échéance dans les 3 prochains jours
- **Destinataires** : Assigné
- **Fréquence** : Quotidienne

### ACTION_ASSIGNED
- **Déclencheur** : Nouvelle action assignée à un utilisateur
- **Destinataires** : Assigné
- **Fréquence** : Immédiate

### ACTION_COMPLETED
- **Déclencheur** : Action marquée comme terminée
- **Destinataires** : Créateur (si différent de l'assigné)
- **Fréquence** : Immédiate

## Configuration

### Variables d'environnement

```env
# Secret pour l'authentification des cron jobs
CRON_SECRET=your-secret-key

# Configuration pour les notifications (optionnel)
NEXT_PUBLIC_CRON_SECRET=your-public-secret-key
```

### Tâches planifiées

Pour automatiser les vérifications, configurez un cron job :

```bash
# Vérifier les notifications toutes les heures
0 * * * * curl -X POST -H "Authorization: Bearer your-secret-key" https://your-domain.com/api/notifications/check
```

## Utilisation

### Intégration dans les composants

```tsx
import { NotificationBell } from "@/components/features/notifications"

// Dans la navbar
<NotificationBell userId={user.id} />
```

```tsx
import { DueActionsDashboard } from "@/components/features/notifications"

// Dans le tableau de bord
<DueActionsDashboard showOnlyAssigned={false} />
```

### Utilisation des hooks

```tsx
import { useNotifications } from "@/hooks/use-notifications"

function MyComponent() {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead
  } = useNotifications(userId)

  // Utiliser les notifications...
}
```

## Pages

### `/notifications`
Page dédiée aux notifications avec :
- Vue d'ensemble des statistiques
- Filtres par statut (toutes, non lues, lues)
- Actions de gestion (marquer comme lu, actualiser)

### `/dashboard`
Tableau de bord principal avec :
- Section "Gestion des échéances"
- Statistiques des actions critiques
- Actions rapides

## Fonctionnalités avancées

### Polling automatique
- Les notifications sont actualisées automatiquement toutes les 30 secondes
- Le compteur de notifications non lues est mis à jour toutes les minutes

### Nettoyage automatique
- Les notifications lues de plus de 30 jours sont supprimées automatiquement
- Exécuté lors des vérifications planifiées

### Déduplication
- Évite la création de notifications en double pour la même action et le même type
- Vérification avant création de nouvelle notification

## Développement futur

### Améliorations prévues
1. **Notifications push** - Intégration avec les notifications navigateur
2. **Notifications email** - Envoi d'emails pour les actions critiques
3. **Personnalisation** - Préférences utilisateur pour les types de notifications
4. **Webhooks** - Intégration avec des systèmes externes
5. **Analytics** - Statistiques sur l'efficacité des notifications

### Base de données
Actuellement, les notifications sont simulées en mémoire. Pour la production, implémenter :

```sql
CREATE TABLE notifications (
  id VARCHAR(255) PRIMARY KEY,
  type VARCHAR(50) NOT NULL,
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  action_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255) NOT NULL,
  read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (action_id) REFERENCES actions(id),
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## Dépannage

### Problèmes courants

1. **Notifications non affichées**
   - Vérifier que l'utilisateur est connecté
   - Vérifier les permissions
   - Consulter les logs du navigateur

2. **Vérifications non exécutées**
   - Vérifier la configuration du cron job
   - Vérifier le secret d'authentification
   - Consulter les logs serveur

3. **Performance**
   - Limiter le nombre de notifications chargées
   - Implémenter la pagination pour les grandes listes
   - Optimiser les requêtes de base de données

### Logs et monitoring

Les logs sont disponibles dans :
- Console navigateur (côté client)
- Logs serveur (côté API)
- Endpoint de statut : `GET /api/notifications/check`

## Support

Pour toute question ou problème :
1. Consulter cette documentation
2. Vérifier les logs d'erreur
3. Tester l'endpoint de statut
4. Contacter l'équipe de développement
