"use client"

import { useState, useEffect, useCallback } from "react"
import { ReportFilters, ReportWithRelations, ReportStats } from "@/lib/validations/report"

interface UseReportsResult {
  reports: ReportWithRelations[]
  total: number
  page: number
  limit: number
  totalPages: number
  loading: boolean
  error: string | null
  filters: ReportFilters
  setFilters: (filters: Partial<ReportFilters>) => void
  refetch: () => Promise<void>
}

interface UseReportsOptions {
  auditId?: string
  organizationId?: string
  creatorId?: string
  initialFilters?: Partial<ReportFilters>
  autoFetch?: boolean
}

const defaultFilters: ReportFilters = {
  page: 1,
  limit: 10,
  sortBy: "createdAt",
  sortOrder: "desc"
}

export function useReports(options: UseReportsOptions = {}): UseReportsResult {
  const {
    auditId,
    organizationId,
    creatorId,
    initialFilters = {},
    autoFetch = true
  } = options

  const [reports, setReports] = useState<ReportWithRelations[]>([])
  const [total, setTotal] = useState(0)
  const [page, setPage] = useState(1)
  const [limit, setLimit] = useState(10)
  const [totalPages, setTotalPages] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const [filters, setFiltersState] = useState<ReportFilters>({
    ...defaultFilters,
    ...initialFilters,
    ...(auditId && { auditId }),
    ...(organizationId && { organizationId }),
    ...(creatorId && { creatorId })
  })

  const setFilters = useCallback((newFilters: Partial<ReportFilters>) => {
    setFiltersState(prev => ({
      ...prev,
      ...newFilters,
      page: newFilters.page || 1 // Reset page when filters change
    }))
  }, [])

  const fetchReports = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Construire les paramètres de requête
      const searchParams = new URLSearchParams()
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          searchParams.append(key, value.toString())
        }
      })

      const response = await fetch(`/api/reports?${searchParams.toString()}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erreur lors du chargement des rapports")
      }

      const data = await response.json()
      
      if (data.success) {
        setReports(data.data.reports)
        setTotal(data.data.total)
        setPage(data.data.page)
        setLimit(data.data.limit)
        setTotalPages(data.data.totalPages)
      } else {
        throw new Error(data.error || "Erreur lors du chargement des rapports")
      }
    } catch (err) {
      console.error("Erreur lors du chargement des rapports:", err)
      setError(err instanceof Error ? err.message : "Erreur inconnue")
      setReports([])
      setTotal(0)
      setTotalPages(0)
    } finally {
      setLoading(false)
    }
  }, [filters])

  // Charger les rapports quand les filtres changent
  useEffect(() => {
    if (autoFetch) {
      fetchReports()
    }
  }, [fetchReports, autoFetch])

  return {
    reports,
    total,
    page,
    limit,
    totalPages,
    loading,
    error,
    filters,
    setFilters,
    refetch: fetchReports
  }
}

// Hook spécialisé pour les rapports d'un audit
export function useAuditReports(auditId: string) {
  return useReports({ auditId })
}

// Hook spécialisé pour les rapports d'une organisation
export function useOrganizationReports(organizationId: string) {
  return useReports({ organizationId })
}

// Hook spécialisé pour les rapports d'un utilisateur
export function useUserReports(creatorId: string) {
  return useReports({ creatorId })
}

// Hook pour les statistiques des rapports
export function useReportStats(organizationId?: string) {
  const [stats, setStats] = useState<ReportStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const searchParams = new URLSearchParams()
      if (organizationId) {
        searchParams.append('organizationId', organizationId)
      }

      const response = await fetch(`/api/reports/stats?${searchParams.toString()}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Erreur lors du chargement des statistiques")
      }

      const data = await response.json()
      
      if (data.success) {
        setStats(data.data)
      } else {
        throw new Error(data.error || "Erreur lors du chargement des statistiques")
      }
    } catch (err) {
      console.error("Erreur lors du chargement des statistiques:", err)
      setError(err instanceof Error ? err.message : "Erreur inconnue")
      setStats(null)
    } finally {
      setLoading(false)
    }
  }, [organizationId])

  useEffect(() => {
    fetchStats()
  }, [fetchStats])

  return {
    stats,
    loading,
    error,
    refetch: fetchStats
  }
}
