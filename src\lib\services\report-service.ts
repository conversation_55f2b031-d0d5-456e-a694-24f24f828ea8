import { prisma } from "@/lib/prisma"
import { 
  CreateReportInput, 
  UpdateReportInput, 
  ReportFilters, 
  GenerateReportInput,
  ExportReportInput,
  PublishReportInput,
  ReportWithRelations,
  ReportStats,
  ReportStatus,
  ReportType
} from "@/lib/validations/report"
import { Prisma } from "@prisma/client"

export class ReportService {
  /**
   * Créer un nouveau rapport
   */
  static async createReport(data: CreateReportInput, creatorId: string): Promise<ReportWithRelations> {
    // Vérifier que l'audit existe
    const audit = await prisma.audit.findUnique({
      where: { id: data.auditId },
      include: { organization: true }
    })

    if (!audit) {
      throw new Error("Audit non trouvé")
    }

    // Vérifier que l'organisation correspond
    if (audit.organizationId !== data.organizationId) {
      throw new Error("L'audit ne correspond pas à l'organisation spécifiée")
    }

    const report = await prisma.report.create({
      data: {
        title: data.title,
        content: data.content,
        auditId: data.auditId,
        organizationId: data.organizationId,
        creatorId,
        status: ReportStatus.DRAFT
      },
      include: {
        organization: {
          select: { id: true, name: true }
        },
        audit: {
          select: { 
            id: true, 
            title: true, 
            status: true, 
            startDate: true, 
            endDate: true 
          }
        },
        creator: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    return report as ReportWithRelations
  }

  /**
   * Obtenir un rapport par ID
   */
  static async getReportById(id: string): Promise<ReportWithRelations | null> {
    const report = await prisma.report.findUnique({
      where: { id },
      include: {
        organization: {
          select: { id: true, name: true }
        },
        audit: {
          select: { 
            id: true, 
            title: true, 
            status: true, 
            startDate: true, 
            endDate: true 
          }
        },
        creator: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    return report as ReportWithRelations | null
  }

  /**
   * Obtenir la liste des rapports avec filtres et pagination
   */
  static async getReports(filters: ReportFilters): Promise<{
    reports: ReportWithRelations[]
    total: number
    page: number
    limit: number
    totalPages: number
  }> {
    const { page, limit, sortBy, sortOrder, search, ...filterParams } = filters

    // Construire les conditions de filtrage
    const where: Prisma.ReportWhereInput = {}

    if (filterParams.status) {
      where.status = filterParams.status
    }

    if (filterParams.organizationId) {
      where.organizationId = filterParams.organizationId
    }

    if (filterParams.auditId) {
      where.auditId = filterParams.auditId
    }

    if (filterParams.creatorId) {
      where.creatorId = filterParams.creatorId
    }

    if (filterParams.createdFrom || filterParams.createdTo) {
      where.createdAt = {}
      if (filterParams.createdFrom) {
        where.createdAt.gte = new Date(filterParams.createdFrom)
      }
      if (filterParams.createdTo) {
        where.createdAt.lte = new Date(filterParams.createdTo)
      }
    }

    if (filterParams.publishedFrom || filterParams.publishedTo) {
      where.publishedAt = {}
      if (filterParams.publishedFrom) {
        where.publishedAt.gte = new Date(filterParams.publishedFrom)
      }
      if (filterParams.publishedTo) {
        where.publishedAt.lte = new Date(filterParams.publishedTo)
      }
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Calculer le total
    const total = await prisma.report.count({ where })

    // Obtenir les rapports avec pagination
    const reports = await prisma.report.findMany({
      where,
      include: {
        organization: {
          select: { id: true, name: true }
        },
        audit: {
          select: { 
            id: true, 
            title: true, 
            status: true, 
            startDate: true, 
            endDate: true 
          }
        },
        creator: {
          select: { id: true, name: true, email: true }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip: (page - 1) * limit,
      take: limit
    })

    return {
      reports: reports as ReportWithRelations[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  }

  /**
   * Mettre à jour un rapport
   */
  static async updateReport(id: string, data: UpdateReportInput): Promise<ReportWithRelations> {
    const updateData: Prisma.ReportUpdateInput = {}

    if (data.title !== undefined) updateData.title = data.title
    if (data.content !== undefined) updateData.content = data.content
    if (data.status !== undefined) updateData.status = data.status

    const report = await prisma.report.update({
      where: { id },
      data: updateData,
      include: {
        organization: {
          select: { id: true, name: true }
        },
        audit: {
          select: { 
            id: true, 
            title: true, 
            status: true, 
            startDate: true, 
            endDate: true 
          }
        },
        creator: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    return report as ReportWithRelations
  }

  /**
   * Supprimer un rapport
   */
  static async deleteReport(id: string): Promise<void> {
    await prisma.report.delete({
      where: { id }
    })
  }

  /**
   * Publier un rapport
   */
  static async publishReport(id: string, data: PublishReportInput): Promise<ReportWithRelations> {
    const publishedAt = data.publishedAt ? new Date(data.publishedAt) : new Date()

    const report = await prisma.report.update({
      where: { id },
      data: {
        status: ReportStatus.PUBLISHED,
        publishedAt
      },
      include: {
        organization: {
          select: { id: true, name: true }
        },
        audit: {
          select: { 
            id: true, 
            title: true, 
            status: true, 
            startDate: true, 
            endDate: true 
          }
        },
        creator: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    // TODO: Implémenter les notifications aux parties prenantes
    if (data.notifyStakeholders) {
      // Logique de notification à implémenter
    }

    return report as ReportWithRelations
  }

  /**
   * Générer automatiquement un rapport basé sur un audit
   */
  static async generateReport(data: GenerateReportInput, creatorId: string): Promise<ReportWithRelations> {
    // Récupérer l'audit avec toutes ses données
    const audit = await prisma.audit.findUnique({
      where: { id: data.auditId },
      include: {
        organization: true,
        observations: {
          include: {
            actions: {
              include: {
                assignee: { select: { name: true, email: true } }
              }
            }
          }
        },
        auditors: {
          include: {
            user: { select: { name: true, email: true } }
          }
        }
      }
    })

    if (!audit) {
      throw new Error("Audit non trouvé")
    }

    // Générer le contenu selon le type de rapport
    let content = ""
    const title = data.title || `Rapport ${this.getReportTypeLabel(data.type)} - ${audit.title}`

    switch (data.type) {
      case ReportType.AUDIT_SUMMARY:
        content = this.generateAuditSummary(audit, data)
        break
      case ReportType.DETAILED_FINDINGS:
        content = this.generateDetailedFindings(audit, data)
        break
      case ReportType.ACTION_PLAN:
        content = this.generateActionPlan(audit, data)
        break
      case ReportType.COMPLIANCE_REPORT:
        content = this.generateComplianceReport(audit, data)
        break
      case ReportType.EXECUTIVE_SUMMARY:
        content = this.generateExecutiveSummary(audit, data)
        break
      default:
        content = this.generateAuditSummary(audit, data)
    }

    // Créer le rapport
    const report = await this.createReport({
      title,
      content,
      auditId: data.auditId,
      organizationId: audit.organizationId,
      type: data.type,
      includeObservations: data.includeObservations,
      includeActions: data.includeActions,
      includeStatistics: data.includeStatistics,
      template: data.template
    }, creatorId)

    // Auto-publier si demandé
    if (data.autoPublish) {
      return this.publishReport(report.id, { notifyStakeholders: false })
    }

    return report
  }

  /**
   * Exporter un rapport dans le format spécifié
   */
  static async exportReport(id: string, data: ExportReportInput): Promise<Buffer> {
    const report = await this.getReportById(id)

    if (!report) {
      throw new Error("Rapport non trouvé")
    }

    switch (data.format) {
      case "PDF":
        return this.exportToPDF(report, data)
      case "WORD":
        return this.exportToWord(report, data)
      case "EXCEL":
        return this.exportToExcel(report, data)
      case "HTML":
        return this.exportToHTML(report, data)
      default:
        throw new Error("Format d'export non supporté")
    }
  }

  /**
   * Obtenir les statistiques des rapports
   */
  static async getReportStats(organizationId?: string): Promise<ReportStats> {
    const where: Prisma.ReportWhereInput = organizationId ? { organizationId } : {}

    const [total, byStatus, published, draft] = await Promise.all([
      // Total des rapports
      prisma.report.count({ where }),

      // Par statut
      prisma.report.groupBy({
        by: ['status'],
        where,
        _count: true
      }),

      // Publiés
      prisma.report.count({
        where: {
          ...where,
          status: ReportStatus.PUBLISHED
        }
      }),

      // Brouillons
      prisma.report.count({
        where: {
          ...where,
          status: ReportStatus.DRAFT
        }
      })
    ])

    const statusCounts = byStatus.reduce((acc, item) => {
      acc[item.status as keyof typeof ReportStatus] = item._count
      return acc
    }, {} as Record<keyof typeof ReportStatus, number>)

    // Valeurs par défaut pour les statuts manquants
    Object.values(ReportStatus).forEach(status => {
      if (!(status in statusCounts)) {
        statusCounts[status as keyof typeof ReportStatus] = 0
      }
    })

    return {
      total,
      byStatus: statusCounts,
      byType: {}, // TODO: Implémenter quand le type sera ajouté au schéma
      published,
      draft,
      averageGenerationTime: 0, // TODO: Implémenter le tracking du temps
      totalExports: 0 // TODO: Implémenter le tracking des exports
    }
  }

  // Méthodes privées pour la génération de contenu
  private static generateAuditSummary(audit: any, options: GenerateReportInput): string {
    let content = `# Résumé d'audit - ${audit.title}\n\n`
    content += `**Organisation:** ${audit.organization.name}\n`
    content += `**Période:** ${audit.startDate.toLocaleDateString()} - ${audit.endDate?.toLocaleDateString() || 'En cours'}\n`
    content += `**Statut:** ${audit.status}\n\n`

    if (options.includeStatistics) {
      content += `## Statistiques\n`
      content += `- Nombre d'observations: ${audit.observations.length}\n`
      content += `- Nombre d'actions: ${audit.observations.reduce((acc: number, obs: any) => acc + obs.actions.length, 0)}\n\n`
    }

    if (options.includeObservations && audit.observations.length > 0) {
      content += `## Observations principales\n`
      audit.observations.forEach((obs: any, index: number) => {
        content += `${index + 1}. **${obs.title}** (${obs.severity})\n`
        content += `   ${obs.description}\n\n`
      })
    }

    return content
  }

  private static generateDetailedFindings(audit: any, options: GenerateReportInput): string {
    // TODO: Implémenter la génération détaillée
    return this.generateAuditSummary(audit, options)
  }

  private static generateActionPlan(audit: any, options: GenerateReportInput): string {
    // TODO: Implémenter la génération du plan d'action
    return this.generateAuditSummary(audit, options)
  }

  private static generateComplianceReport(audit: any, options: GenerateReportInput): string {
    // TODO: Implémenter la génération du rapport de conformité
    return this.generateAuditSummary(audit, options)
  }

  private static generateExecutiveSummary(audit: any, options: GenerateReportInput): string {
    // TODO: Implémenter la génération du résumé exécutif
    return this.generateAuditSummary(audit, options)
  }

  private static getReportTypeLabel(type: string): string {
    const labels: Record<string, string> = {
      [ReportType.AUDIT_SUMMARY]: "Résumé d'audit",
      [ReportType.DETAILED_FINDINGS]: "Constats détaillés",
      [ReportType.ACTION_PLAN]: "Plan d'action",
      [ReportType.COMPLIANCE_REPORT]: "Rapport de conformité",
      [ReportType.EXECUTIVE_SUMMARY]: "Résumé exécutif"
    }
    return labels[type] || "Rapport"
  }

  // Méthodes privées pour l'export (à implémenter)
  private static async exportToPDF(report: ReportWithRelations, options: ExportReportInput): Promise<Buffer> {
    // TODO: Implémenter l'export PDF avec puppeteer ou jsPDF
    throw new Error("Export PDF non encore implémenté")
  }

  private static async exportToWord(report: ReportWithRelations, options: ExportReportInput): Promise<Buffer> {
    // TODO: Implémenter l'export Word avec docx
    throw new Error("Export Word non encore implémenté")
  }

  private static async exportToExcel(report: ReportWithRelations, options: ExportReportInput): Promise<Buffer> {
    // TODO: Implémenter l'export Excel avec exceljs
    throw new Error("Export Excel non encore implémenté")
  }

  private static async exportToHTML(report: ReportWithRelations, options: ExportReportInput): Promise<Buffer> {
    // TODO: Implémenter l'export HTML
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${report.title}</title>
          <meta charset="utf-8">
        </head>
        <body>
          <h1>${report.title}</h1>
          <div>${report.content}</div>
        </body>
      </html>
    `
    return Buffer.from(html, 'utf-8')
  }
}
