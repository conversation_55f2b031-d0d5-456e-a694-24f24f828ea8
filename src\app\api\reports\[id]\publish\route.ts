import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { ReportService } from "@/lib/services/report-service"
import { publishReportSchema } from "@/lib/validations/report"
import { hasPermission } from "@/lib/validations/user"

type UserRole = "SUPER_ADMIN" | "ADMIN" | "MANAGER" | "AUDITOR" | "USER"
import { z } from "zod"

/**
 * POST /api/reports/[id]/publish - Publier un rapport
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions - seuls les managers et admins peuvent publier
      if (!hasPermission(user.role as UserRole, 'reports', 'update') || 
          !["SUPER_ADMIN", "ADMIN", "MANAGER"].includes(user.role)) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes pour publier" },
          { status: 403 }
        )
      }

      // Vérifier que le rapport existe et que l'utilisateur y a accès
      const report = await ReportService.getReportById(params.id)
      
      if (!report) {
        return NextResponse.json(
          { success: false, error: "Rapport non trouvé" },
          { status: 404 }
        )
      }

      // Vérifier l'accès organisationnel
      if (user.role !== "SUPER_ADMIN" && user.organizationId !== report.organizationId) {
        return NextResponse.json(
          { success: false, error: "Accès non autorisé" },
          { status: 403 }
        )
      }

      // Vérifier que le rapport peut être publié
      if (report.status === "PUBLISHED") {
        return NextResponse.json(
          { success: false, error: "Le rapport est déjà publié" },
          { status: 400 }
        )
      }

      const body = await req.json()
      const validatedData = publishReportSchema.parse(body)

      const publishedReport = await ReportService.publishReport(params.id, validatedData)

      return NextResponse.json({
        success: true,
        data: publishedReport,
        message: "Rapport publié avec succès"
      })

    } catch (error) {
      console.error("Erreur lors de la publication du rapport:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Données invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
