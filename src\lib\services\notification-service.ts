import { prisma } from "@/lib/prisma"
import { ActionService } from "./action-service"
import { ActionWithRelations, ActionStatus, getDaysUntilDue, isActionOverdue } from "@/lib/validations/action"

export interface NotificationData {
  id: string
  type: 'ACTION_OVERDUE' | 'ACTION_DUE_SOON' | 'ACTION_ASSIGNED' | 'ACTION_COMPLETED'
  title: string
  message: string
  actionId: string
  userId: string
  read: boolean
  createdAt: Date
  action?: ActionWithRelations
}

export interface NotificationFilters {
  userId?: string
  type?: NotificationData['type']
  read?: boolean
  limit?: number
  offset?: number
}

export class NotificationService {
  /**
   * Créer une notification
   */
  static async createNotification(data: {
    type: NotificationData['type']
    title: string
    message: string
    actionId: string
    userId: string
  }): Promise<NotificationData> {
    // Pour l'instant, on simule avec une structure en mémoire
    // Dans une vraie application, on utiliserait une table notifications
    const notification: NotificationData = {
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: data.type,
      title: data.title,
      message: data.message,
      actionId: data.actionId,
      userId: data.userId,
      read: false,
      createdAt: new Date()
    }

    // TODO: Sauvegarder en base de données
    // await prisma.notification.create({ data: notification })

    return notification
  }

  /**
   * Obtenir les notifications d'un utilisateur
   */
  static async getUserNotifications(filters: NotificationFilters): Promise<NotificationData[]> {
    // Pour l'instant, on retourne un tableau vide
    // Dans une vraie application, on récupérerait depuis la base
    return []
  }

  /**
   * Marquer une notification comme lue
   */
  static async markAsRead(notificationId: string): Promise<void> {
    // TODO: Mettre à jour en base
    // await prisma.notification.update({
    //   where: { id: notificationId },
    //   data: { read: true }
    // })
  }

  /**
   * Marquer toutes les notifications d'un utilisateur comme lues
   */
  static async markAllAsRead(userId: string): Promise<void> {
    // TODO: Mettre à jour en base
    // await prisma.notification.updateMany({
    //   where: { userId, read: false },
    //   data: { read: true }
    // })
  }

  /**
   * Vérifier les actions en retard et créer des notifications
   */
  static async checkOverdueActions(): Promise<void> {
    try {
      const overdueActions = await ActionService.getOverdueActions()
      
      for (const action of overdueActions) {
        // Vérifier si une notification pour cette action existe déjà
        const existingNotification = await this.getActionNotification(action.id, 'ACTION_OVERDUE')
        
        if (!existingNotification) {
          const daysOverdue = Math.abs(getDaysUntilDue(action.dueDate))
          
          await this.createNotification({
            type: 'ACTION_OVERDUE',
            title: 'Action en retard',
            message: `L'action "${action.title}" est en retard de ${daysOverdue} jour${daysOverdue > 1 ? 's' : ''}`,
            actionId: action.id,
            userId: action.assigneeId
          })

          // Notifier aussi le créateur si différent de l'assigné
          if (action.creatorId !== action.assigneeId) {
            await this.createNotification({
              type: 'ACTION_OVERDUE',
              title: 'Action en retard',
              message: `L'action "${action.title}" assignée à ${action.assignee.name || action.assignee.email} est en retard de ${daysOverdue} jour${daysOverdue > 1 ? 's' : ''}`,
              actionId: action.id,
              userId: action.creatorId
            })
          }
        }
      }
    } catch (error) {
      console.error('Erreur lors de la vérification des actions en retard:', error)
    }
  }

  /**
   * Vérifier les actions qui arrivent à échéance bientôt
   */
  static async checkUpcomingDueActions(): Promise<void> {
    try {
      // Récupérer les actions qui arrivent à échéance dans les 3 prochains jours
      const upcomingActions = await prisma.action.findMany({
        where: {
          status: {
            in: [ActionStatus.PENDING, ActionStatus.IN_PROGRESS]
          },
          dueDate: {
            gte: new Date(),
            lte: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 jours
          }
        },
        include: {
          assignee: {
            select: { id: true, name: true, email: true }
          },
          creator: {
            select: { id: true, name: true, email: true }
          },
          audit: {
            select: { id: true, title: true, status: true }
          },
          observation: {
            select: { id: true, title: true, severity: true }
          }
        }
      })

      for (const action of upcomingActions) {
        const daysUntilDue = getDaysUntilDue(action.dueDate)
        
        // Vérifier si une notification pour cette échéance existe déjà
        const existingNotification = await this.getActionNotification(action.id, 'ACTION_DUE_SOON')
        
        if (!existingNotification && daysUntilDue <= 3) {
          let message = ''
          if (daysUntilDue === 0) {
            message = `L'action "${action.title}" arrive à échéance aujourd'hui`
          } else if (daysUntilDue === 1) {
            message = `L'action "${action.title}" arrive à échéance demain`
          } else {
            message = `L'action "${action.title}" arrive à échéance dans ${daysUntilDue} jours`
          }

          await this.createNotification({
            type: 'ACTION_DUE_SOON',
            title: 'Échéance proche',
            message,
            actionId: action.id,
            userId: action.assigneeId
          })
        }
      }
    } catch (error) {
      console.error('Erreur lors de la vérification des échéances:', error)
    }
  }

  /**
   * Notifier l'assignation d'une action
   */
  static async notifyActionAssigned(action: ActionWithRelations): Promise<void> {
    try {
      await this.createNotification({
        type: 'ACTION_ASSIGNED',
        title: 'Nouvelle action assignée',
        message: `Une nouvelle action "${action.title}" vous a été assignée`,
        actionId: action.id,
        userId: action.assigneeId
      })
    } catch (error) {
      console.error('Erreur lors de la notification d\'assignation:', error)
    }
  }

  /**
   * Notifier la completion d'une action
   */
  static async notifyActionCompleted(action: ActionWithRelations): Promise<void> {
    try {
      // Notifier le créateur si différent de l'assigné
      if (action.creatorId !== action.assigneeId) {
        await this.createNotification({
          type: 'ACTION_COMPLETED',
          title: 'Action terminée',
          message: `L'action "${action.title}" a été marquée comme terminée par ${action.assignee.name || action.assignee.email}`,
          actionId: action.id,
          userId: action.creatorId
        })
      }
    } catch (error) {
      console.error('Erreur lors de la notification de completion:', error)
    }
  }

  /**
   * Vérifier si une notification existe pour une action et un type donnés
   */
  private static async getActionNotification(actionId: string, type: NotificationData['type']): Promise<NotificationData | null> {
    // TODO: Implémenter la recherche en base
    // return await prisma.notification.findFirst({
    //   where: { actionId, type }
    // })
    return null
  }

  /**
   * Exécuter toutes les vérifications de notifications
   */
  static async runNotificationChecks(): Promise<void> {
    console.log('Exécution des vérifications de notifications...')
    
    await Promise.all([
      this.checkOverdueActions(),
      this.checkUpcomingDueActions()
    ])
    
    console.log('Vérifications de notifications terminées')
  }

  /**
   * Obtenir le nombre de notifications non lues pour un utilisateur
   */
  static async getUnreadCount(userId: string): Promise<number> {
    // TODO: Implémenter le comptage en base
    // return await prisma.notification.count({
    //   where: { userId, read: false }
    // })
    return 0
  }

  /**
   * Supprimer les anciennes notifications (plus de 30 jours)
   */
  static async cleanupOldNotifications(): Promise<void> {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      
      // TODO: Implémenter la suppression en base
      // await prisma.notification.deleteMany({
      //   where: {
      //     createdAt: { lt: thirtyDaysAgo },
      //     read: true
      //   }
      // })
      
      console.log('Nettoyage des anciennes notifications terminé')
    } catch (error) {
      console.error('Erreur lors du nettoyage des notifications:', error)
    }
  }
}
