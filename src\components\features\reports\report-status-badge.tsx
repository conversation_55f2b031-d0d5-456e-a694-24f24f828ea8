import { Badge } from "@/components/ui/badge"
import { ReportStatusType, getReportStatusLabel, getReportStatusColor } from "@/lib/validations/report"

interface ReportStatusBadgeProps {
  status: ReportStatusType
  className?: string
}

export function ReportStatusBadge({ status, className }: ReportStatusBadgeProps) {
  return (
    <Badge 
      variant="secondary" 
      className={`${getReportStatusColor(status)} ${className}`}
    >
      {getReportStatusLabel(status)}
    </Badge>
  )
}
