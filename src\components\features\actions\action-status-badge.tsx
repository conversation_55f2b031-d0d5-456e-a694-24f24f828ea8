import { Badge } from "@/components/ui/badge"
import { ActionStatus, getActionStatusLabel, getActionStatusColor } from "@/lib/validations/action"
import { Clock, Play, CheckCircle, XCircle, AlertTriangle } from "lucide-react"

interface ActionStatusBadgeProps {
  status: ActionStatus
  className?: string
}

export function ActionStatusBadge({ status, className }: ActionStatusBadgeProps) {
  const label = getActionStatusLabel(status)
  const colorClasses = getActionStatusColor(status)

  const getIcon = () => {
    switch (status) {
      case ActionStatus.PENDING:
        return <Clock className="h-3 w-3 mr-1" />
      case ActionStatus.IN_PROGRESS:
        return <Play className="h-3 w-3 mr-1" />
      case ActionStatus.COMPLETED:
        return <CheckCircle className="h-3 w-3 mr-1" />
      case ActionStatus.CANCELLED:
        return <XCircle className="h-3 w-3 mr-1" />
      case ActionStatus.OVERDUE:
        return <AlertTriangle className="h-3 w-3 mr-1" />
      default:
        return null
    }
  }

  return (
    <Badge 
      variant="outline" 
      className={`${colorClasses} ${className}`}
    >
      {getIcon()}
      {label}
    </Badge>
  )
}
