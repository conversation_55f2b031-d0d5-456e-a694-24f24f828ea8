"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { ActionForm } from "@/components/features/actions/action-form"
import { useActionActions } from "@/hooks/use-action-actions"
import { useFormData } from "@/hooks/use-form-data"
import { useActionPermissions } from "@/hooks/use-action-permissions"
import { UpdateActionInput, ActionWithRelations } from "@/lib/validations/action"
import { ArrowLeft, AlertCircle, Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"

interface EditActionPageProps {
  params: {
    id: string
  }
}

export default function EditActionPage({ params }: EditActionPageProps) {
  const router = useRouter()
  const [action, setAction] = useState<ActionWithRelations | null>(null)

  const {
    loading: actionLoading,
    error: actionError,
    getAction,
    updateAction,
    clearError
  } = useActionActions()

  const {
    users,
    loading: dataLoading,
    error: dataError
  } = useFormData()

  const {
    canUpdate
  } = useActionPermissions(action || undefined)

  useEffect(() => {
    const fetchAction = async () => {
      const actionData = await getAction(params.id)
      if (actionData) {
        setAction(actionData)
      }
    }

    fetchAction()
  }, [params.id, getAction])

  const handleSubmit = async (data: UpdateActionInput) => {
    const updatedAction = await updateAction(params.id, data)
    if (updatedAction) {
      router.push(`/actions/${updatedAction.id}`)
    }
  }

  const loading = dataLoading || actionLoading || !action

  // Vérifier les permissions
  if (action && !canUpdate(action)) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold magneto-title">Modifier l'action</h1>
            <p className="text-gray-600">Permissions insuffisantes</p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Vous n'avez pas les permissions nécessaires pour modifier cette action.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" disabled>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold magneto-title">Modifier l'action</h1>
            <p className="text-gray-600">Chargement...</p>
          </div>
        </div>

        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (dataError || actionError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          <div>
            <h1 className="text-3xl font-bold magneto-title">Modifier l'action</h1>
            <p className="text-gray-600">Erreur lors du chargement</p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {dataError || actionError}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-2"
            >
              Fermer
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!action) {
    return (
      <div className="space-y-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Action non trouvée
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Retour
        </Button>

        <div>
          <h1 className="text-3xl font-bold magneto-title">Modifier l'action</h1>
          <p className="text-gray-600">
            {action.title}
          </p>
        </div>
      </div>

      {/* Informations contextuelles */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Cette action est liée à l'audit "{action.audit.title}"
          {action.observation && ` et à l'observation "${action.observation.title}"`}.
        </AlertDescription>
      </Alert>

      {/* Formulaire */}
      <ActionForm
        action={action}
        users={users}
        onSubmit={handleSubmit}
        loading={actionLoading}
        error={actionError}
      />
    </div>
  )
}
